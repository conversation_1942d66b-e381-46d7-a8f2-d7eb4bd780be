resources:
  repositories:
    - repository: QualityChecksRepo
      type: git
      name: Back-End/Hubtel.CodeQuality.Pipelines
      ref: master

trigger:
  - master
  - staging

schedules:
- cron: "0 0 * * *"  # Runs every day at midnight UTC
  displayName: DailyMidnight
  branches:
    include:
    - master
    - staging

variables:
  - group: HubtelDockerBuildVariables
  - name: buildConfiguration
    value: 'Release'

extends:
  template: backend-repos/main-staged.yml@QualityChecksRepo
  parameters:
    SonarQubeProjectKey: "Orders_Order-Consumer-OrderPaid_AYq1sZZ5o5AhFR4ORcUT"
    ProjectName: "Order Consumer OrderPaid"
    SonarQubeServiceConnection: "Hubtel_SonarQube_Azure-Orders"
    DotnetVersion: '3.x,6.x,8.x'
    PublishArtifacts: true
    BuildAgent: AZPoolNew-SonarQube
    HasUnitTest: false

