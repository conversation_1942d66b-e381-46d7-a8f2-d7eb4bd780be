
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.33103.201
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{AFAE6489-39FE-41B9-A0AF-DE90500BBB0A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{C94BF185-7B71-4FAD-982C-DEB998F0463C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Hubtel.Orders.Consumers.PaymentReceived", "src\Hubtel.Orders.Consumers.OrderPaid\Hubtel.Orders.Consumers.PaymentReceived.csproj", "{DFABF8EF-F4E6-42EE-A49D-B721A5AE267C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{A555BABC-87FF-4867-9D29-2F342A8CC3CC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Plugins", "Plugins", "{60F7D793-136F-40E0-9671-810B51C096F9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Hubtel.Orders.Consumers.Common", "src\Hubtel.Orders.Consumers.Common\Hubtel.Orders.Consumers.Common.csproj", "{D07125C9-7E8B-48A1-8350-D26E4490DBC3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Hubtel.Orders.Consumers.Common.Actors", "src\Hubtel.Orders.Consumers.Common.Actors\Hubtel.Orders.Consumers.Common.Actors.csproj", "{E3BB9757-AF41-439F-8DA5-2CD0F1C9F49D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Hubtel.Orders.Consumers.Plugins.Postgres", "src\Hubtel.Orders.Consumers.Plugins.Postgres\Hubtel.Orders.Consumers.Plugins.Postgres.csproj", "{A45292BC-9443-4013-9EAF-F7C3FDB23119}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Hubtel.Orders.Consumers.Storage.Pg", "src\Hubtel.Orders.Consumers.Storage.Pg\Hubtel.Orders.Consumers.Storage.Pg.csproj", "{184DA0AB-542B-441B-BE13-0D3D93A57FFF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Hubtel.Orders.Consumers.SavedOrders", "src\Hubtel.Orders.Consumers.SavedOrders\Hubtel.Orders.Consumers.SavedOrders.csproj", "{CD519E3B-8C3C-4E1E-8D81-F3B96401BDC8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Hubtel.Orders.Consumers.Paylink", "src\Hubtel.Orders.Consumers.Paylink\Hubtel.Orders.Consumers.Paylink.csproj", "{CE8DC81C-C372-4F41-950E-31C037FB35B8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{2CB4B44B-69E4-4160-B08F-14CBFCAC2AD2}"
	ProjectSection(SolutionItems) = preProject
		azure-pipelines.yml = azure-pipelines.yml
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Hubtel.Orders.Orderpaid.DB.Consumer", "src\Hubtel.Orders.Orderpaid.DB.Consumer\Hubtel.Orders.Orderpaid.DB.Consumer.csproj", "{A0BEA0A7-FB53-43E3-8DCC-ADE44339FE98}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DFABF8EF-F4E6-42EE-A49D-B721A5AE267C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DFABF8EF-F4E6-42EE-A49D-B721A5AE267C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DFABF8EF-F4E6-42EE-A49D-B721A5AE267C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DFABF8EF-F4E6-42EE-A49D-B721A5AE267C}.Release|Any CPU.Build.0 = Release|Any CPU
		{D07125C9-7E8B-48A1-8350-D26E4490DBC3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D07125C9-7E8B-48A1-8350-D26E4490DBC3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D07125C9-7E8B-48A1-8350-D26E4490DBC3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D07125C9-7E8B-48A1-8350-D26E4490DBC3}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3BB9757-AF41-439F-8DA5-2CD0F1C9F49D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3BB9757-AF41-439F-8DA5-2CD0F1C9F49D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3BB9757-AF41-439F-8DA5-2CD0F1C9F49D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3BB9757-AF41-439F-8DA5-2CD0F1C9F49D}.Release|Any CPU.Build.0 = Release|Any CPU
		{A45292BC-9443-4013-9EAF-F7C3FDB23119}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A45292BC-9443-4013-9EAF-F7C3FDB23119}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A45292BC-9443-4013-9EAF-F7C3FDB23119}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A45292BC-9443-4013-9EAF-F7C3FDB23119}.Release|Any CPU.Build.0 = Release|Any CPU
		{184DA0AB-542B-441B-BE13-0D3D93A57FFF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{184DA0AB-542B-441B-BE13-0D3D93A57FFF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{184DA0AB-542B-441B-BE13-0D3D93A57FFF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{184DA0AB-542B-441B-BE13-0D3D93A57FFF}.Release|Any CPU.Build.0 = Release|Any CPU
		{CD519E3B-8C3C-4E1E-8D81-F3B96401BDC8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CD519E3B-8C3C-4E1E-8D81-F3B96401BDC8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CD519E3B-8C3C-4E1E-8D81-F3B96401BDC8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CD519E3B-8C3C-4E1E-8D81-F3B96401BDC8}.Release|Any CPU.Build.0 = Release|Any CPU
		{CE8DC81C-C372-4F41-950E-31C037FB35B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CE8DC81C-C372-4F41-950E-31C037FB35B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CE8DC81C-C372-4F41-950E-31C037FB35B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CE8DC81C-C372-4F41-950E-31C037FB35B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{A0BEA0A7-FB53-43E3-8DCC-ADE44339FE98}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A0BEA0A7-FB53-43E3-8DCC-ADE44339FE98}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A0BEA0A7-FB53-43E3-8DCC-ADE44339FE98}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A0BEA0A7-FB53-43E3-8DCC-ADE44339FE98}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{DFABF8EF-F4E6-42EE-A49D-B721A5AE267C} = {AFAE6489-39FE-41B9-A0AF-DE90500BBB0A}
		{A555BABC-87FF-4867-9D29-2F342A8CC3CC} = {AFAE6489-39FE-41B9-A0AF-DE90500BBB0A}
		{60F7D793-136F-40E0-9671-810B51C096F9} = {AFAE6489-39FE-41B9-A0AF-DE90500BBB0A}
		{D07125C9-7E8B-48A1-8350-D26E4490DBC3} = {A555BABC-87FF-4867-9D29-2F342A8CC3CC}
		{E3BB9757-AF41-439F-8DA5-2CD0F1C9F49D} = {A555BABC-87FF-4867-9D29-2F342A8CC3CC}
		{A45292BC-9443-4013-9EAF-F7C3FDB23119} = {60F7D793-136F-40E0-9671-810B51C096F9}
		{184DA0AB-542B-441B-BE13-0D3D93A57FFF} = {A555BABC-87FF-4867-9D29-2F342A8CC3CC}
		{CD519E3B-8C3C-4E1E-8D81-F3B96401BDC8} = {AFAE6489-39FE-41B9-A0AF-DE90500BBB0A}
		{CE8DC81C-C372-4F41-950E-31C037FB35B8} = {AFAE6489-39FE-41B9-A0AF-DE90500BBB0A}
		{A0BEA0A7-FB53-43E3-8DCC-ADE44339FE98} = {AFAE6489-39FE-41B9-A0AF-DE90500BBB0A}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {612153F0-07F8-4F16-96A9-0E9C82E1FE0D}
	EndGlobalSection
EndGlobal
