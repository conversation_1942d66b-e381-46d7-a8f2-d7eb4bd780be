using System;

namespace Hubtel.Orders.Consumers.Common
{
    public static class PartitionedQueryHelpers
    {
        public static string OrderPaymentsConstraintQuery()
        {
            var timeConditionalQuery = string.Empty;
            if (MilitaryTime.Hour == 0 && MilitaryTime.Minute  <= 10)
            {
                var dateTime = DateTime.UtcNow;
                
                timeConditionalQuery =
                    $"AND P.\"CreatedAt\" >= '{dateTime.AddDays(-1).StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'" +
                    $"::timestamp without time zone AND P.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'::timestamp without time zone"
                    + $" AND O.\"CreatedAt\" >= '{dateTime.AddDays(-1).StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'" +
                    $"::timestamp without time zone AND O.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'::timestamp without time zone";
            }
            else
            {
                var dateTime = DateTime.UtcNow;
                timeConditionalQuery =
                    $"AND P.\"CreatedAt\" >= '{dateTime.StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'::timestamp" +
                    $" without time zone AND P.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'::timestamp without time zone"
                    + $" AND O.\"CreatedAt\" >= '{dateTime.StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'::timestamp " +
                    $"without time zone AND O.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'::timestamp without time zone";
            }

            return timeConditionalQuery;
        }
        public static string OrderConstraintQuery()
        {
            var timeConditionalQuery = string.Empty;
            if (MilitaryTime.Hour == 0 && MilitaryTime.Minute  <= 10)
            {
                var dateTime = DateTime.UtcNow;
                
                timeConditionalQuery =
                    $"AND O.\"CreatedAt\" >= '{dateTime.AddDays(-1).StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'" +
                    $"::timestamp without time zone AND O.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'" +
                    $"::timestamp without time zone";
            }
            else
            {
                var dateTime = DateTime.UtcNow;
                timeConditionalQuery =
                    $"AND O.\"CreatedAt\" >= '{dateTime.StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'::timestamp without time zone AND O.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'::timestamp without time zone";
            }

            return timeConditionalQuery;
        }
        public static string PaymentsConstraintQuery()
        {
            var timeConditionalQuery = string.Empty;
            if (MilitaryTime.Hour == 0 && MilitaryTime.Minute  <= 10)
            {
                var dateTime = DateTime.UtcNow;
                
                timeConditionalQuery =
                    $"AND P.\"CreatedAt\" >= '{dateTime.AddDays(-1).StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'" +
                    $"::timestamp without time zone AND P.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'::timestamp without time zone";
            }
            else
            {
                var dateTime = DateTime.UtcNow;
                timeConditionalQuery =
                    $"AND P.\"CreatedAt\" >= '{dateTime.StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'" +
                    $"::timestamp without time zone AND P.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'::timestamp without time zone";
            }

            return timeConditionalQuery;
        }
        public static string OrderAndOrderItemsConstraintQuery()
        {
            var timeConditionalQuery = string.Empty;
            if (MilitaryTime.Hour == 0 && MilitaryTime.Minute  <= 10)
            {
                var dateTime = DateTime.UtcNow;
                
                timeConditionalQuery =
                    $"AND O.\"CreatedAt\" >= '{dateTime.AddDays(-1).StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'::timestamp without time zone AND O.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'::timestamp without time zone"
                    + $" AND OI.\"CreatedAt\" >= '{dateTime.AddDays(-1).StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'::timestamp without time zone AND OI.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'::timestamp without time zone";
            }
            else
            {
                var dateTime = DateTime.UtcNow;
                timeConditionalQuery =
                    $"AND O.\"CreatedAt\" >= '{dateTime.StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'::timestamp without time zone AND O.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'::timestamp without time zone"
                    + $" AND OI.\"CreatedAt\" >= '{dateTime.StartOfDay().ToString("yyyy-MM-dd HH:mm:ss")}'::timestamp without time zone AND OI.\"CreatedAt\" <= " +
                    $"'{dateTime.EndOfDay().ToString("yyyy-MM-dd HH:mm:ss.ffffff")}'::timestamp without time zone";
            }

            return timeConditionalQuery;
        }
        
        
        private static DateTime MilitaryTime => DateTime.SpecifyKind(
            DateTime.Parse(DateTime.UtcNow.ToString("h:mm:ss tt zz")),DateTimeKind.Utc);
    }
}