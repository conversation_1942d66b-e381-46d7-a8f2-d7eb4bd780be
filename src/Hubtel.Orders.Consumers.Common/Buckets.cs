namespace Hubtel.Orders.Consumers.Common
{
    public class Buckets
    {
        private Buckets(string name) { Name = name; }

        public string Name { get;}

        public static Buckets PaymentReceived => new Buckets("PaymentReceived");
        public static Buckets CacheBounce => new Buckets("CacheBounce");
        public static Buckets ErrorCount => new Buckets("ErrorCount");
        public static Buckets PaymentCreatedSuccessfully => new Buckets("PaymentCreatedSuccessfully");
        public static Buckets PaymentCreatedSuccessfullyTime => new Buckets("PaymentCreatedSuccessfullyTime");
        public static Buckets PaidOrders => new Buckets("PaidOrders");
        public static Buckets MemoryConsumptionBucket => new Buckets("MemoryConsumptionBucket");
        public static Buckets TemporaryTableBulkInsertTime => new Buckets("TemporaryTableBulkInsertTime");
        public static Buckets CreateTemporaryTable => new Buckets("CreateTemporaryTable");
        public static Buckets OrderPaidPaymentPublishBucket => new Buckets("OrderPaidPaymentPublishBucket");
        public static Buckets BulkUpdateOnOrders => new Buckets("BulkUpdateOnOrders");
        public static Buckets UpdatePaymentInformationFromOrders => new Buckets("UpdatePaymentInformationFromOrders");
        public static Buckets FetchOrderAndOrderItemsTime => new Buckets("FetchOrderAndOrderItemsTime");
        public static Buckets OrderPaidKafkaPublishTime => new Buckets("OrderPaidKafkaPublishTime");
        public static Buckets OrderPaidAndUnpaidKafkaPublishTime => new Buckets("OrderPaidAndUnpaidKafkaPublishTime");
        public static Buckets OrderPaidProgrammableService => new Buckets("OrderPaidProgrammableService");
        public static Buckets OrderPaidProgrammableServiceTime => new Buckets("OrderPaidProgrammableServiceTime");
        public static Buckets OrderPaidAndUnpaidPaymentPublishBucket => new Buckets("OrderPaidAndUnpaidPaymentPublishBucket");
        public static Buckets OrderPaidAndUnpaidEcommercePaymentPublishBucket => new Buckets("OrderPaidAndUnpaidEcommercePaymentPublishBucket");
    }
}