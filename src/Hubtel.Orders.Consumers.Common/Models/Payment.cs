using System;

namespace Hubtel.Orders.Consumers.Common.Models
{
    public class Payment
    {
        public Payment()
        {
            Id = Guid.NewGuid().ToString("N");
        }

        public string Id { get; set; }

        public string BusinessId { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public string CreatedBy { get; set; }

        public string UpdatedBy { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PaymentType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string OrderId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MomoPhoneNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MomoChannel { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MomoToken { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string TransactionId { get; set; } // from merchant account
        /// <summary>
        /// 
        /// </summary>
        public string ExternalTransactionId { get; set; }// from telco to merchant account
        /// <summary>
        /// 
        /// </summary>
        public decimal AmountAfterCharges { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal Charges { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PaymentCategory { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal Tips { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal AmountPlusTips { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal TipSettlement { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool HasTip {get;set;}
        /// <summary>
        /// 
        /// </summary>
        public bool ChargeCustomer { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal AmountPaid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal ElevyAmount { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime PaymentDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Note { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PosDeviceId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PosDeviceType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string EmployeeId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CustomerMobileNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CustomerName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BranchId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BranchName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsRefund { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsSuccessful { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ReceiptNumber { get; set; } //this is autogenerated increment for organization for successful payments
        /// <summary>
        /// 
        /// </summary>
        public string Location { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Currency { get; set; }
        /// <summary>
        /// 
        /// </summary>
        //public virtual Order Order { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Scheme { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Card { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Tid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Authorization { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Mid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CardTransactionId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal AmountTendered { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal Balance { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ClientReference { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ProviderDescription { get; set; }

        public string ProviderResponseCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string StatusCode { get; set; }

        public int FineractSavingsAccountId { get; set; }

        public string CardTransactionMode { get; set; }

        public string CardProcessor { get; set; }
        
        public string CallbackUrl {get; set; }

        /// <summary>
        /// Indicates whether the invoice was created due to a recurring payment
        /// </summary>
        public bool IsRecurring { get; set; }
        /// <summary>
        /// RecurringInvoiceID
        /// </summary>
        public string RecurringInvoiceId { get; set; }
        /// <summary>
        /// If mobile money, this is the transaction id of the send money transaction
        /// </summary>
        public string RefundTransactionId { get; set; }
        /// <summary>
        /// The date the Business requested the refund
        /// </summary>
        public DateTime? RefundRequestedDate { get; set; }
        /// <summary>
        /// The date Hubtel completed the refund
        /// </summary>
        public DateTime? RefundCompletedDate { get; set; }
        /// <summary>
        /// Employee who requested for a Refund
        /// </summary>
        public string RefundRequestedBy { get; set; }
        /// <summary>
        /// MobileMoney or Card
        /// </summary>
        public string RefundDestinationType { get; set; }
        /// <summary>
        /// MobileMoney Number or Masked Card Number
        /// </summary>
        public string RefundDestination { get; set; }
        /// <summary>
        /// Amount that will be refunded.
        /// </summary>
        public decimal AmountRefunded { get; set; }
        /// <summary>
        /// Pending || Completed || Rejected
        /// </summary>
        public string RefundStatus { get; set; }

        public decimal DeliveryFee { get; set; }

        public string PaymentProcessor { get; set; }
        public string CardIssuer { get; set; }
        public string IntegrationChannel { get; set; }
        public string EntryMode { get; set; }
        public decimal TaxWithHolding { get; set; }
        public bool IsBulkSettlement { get; set; }

    }
}
