using System;

namespace Hubtel.Orders.Consumers.Common.Models
{
    /// <summary>
    /// 
    /// </summary>
    public class PaymentReceivedMessage
    {
        public PaymentReceivedMessage()
        {
            Id = Guid.NewGuid().ToString("N");
        }

        public string Id { get; set; }

        public string BusinessId { get; set; }

        public DateTime CreatedAt { get; set; }

        public string PaymentType { get; set; }

        public string OrderId { get; set; }

        public string HubtelAccountId { get; set; }

        public int FineractSavingsAccountId { get; set; }

        public string TransactionId { get; set; }

        public string ExternalTransactionId { get; set; }

        public string CustomerMobileNumber { get; set; }

        public string CustomerName { get; set; }

        public decimal AmountAfterCharges { get; set; }

        public decimal Fee { get; set; }

        public decimal DeliveryFee { get; set; }

        public bool ChargeCustomer { get; set; }

        public decimal AmountPaid { get; set; }

        public DateTime PaymentDate { get; set; }

        public string Description { get; set; }

        public bool IsSuccessful { get; set; }

        public string Currency { get; set; }

        public string Country { get; set; }

        public Card Card { get; set; }

        public MobileMoney MobileMoney { get; set; }
        
        public Cash Cash { get; set; }

        public string CallbackUrl { get; set; }
        public string ClientReference { get; set; }

        public string ProviderDescription { get; set; }

        public string ProviderResponseCode { get; set; }

        public string StatusCode { get; set; }
        public string ReceiptNumber { get; set; }
        public decimal AmountTendered { get; set; }
        public bool IsRefund { get; set; }
        /// <summary>
        /// Indicates whether the invoice was created due to a recurring payment
        /// </summary>
        public bool IsRecurring { get; set; }
        /// <summary>
        /// RecurringInvoiceID
        /// </summary>
        public string RecurringInvoiceId { get; set; }
        /// <summary>
        /// If mobile money, this is the transaction id of the send money transaction
        /// </summary>
        public string RefundTransactionId { get; set; }
        /// <summary>
        /// The date the Business requested the refund
        /// </summary>
        public DateTime? RefundRequestedDate { get; set; }
        /// <summary>
        /// The date Hubtel completed the refund
        /// </summary>
        public DateTime? RefundCompletedDate { get; set; }
        /// <summary>
        /// Employee who requested for a Refund
        /// </summary>
        public string RefundRequestedBy { get; set; }
        /// <summary>
        /// MobileMoney or Card
        /// </summary>
        public string RefundDestinationType { get; set; }
        /// <summary>
        /// MobileMoney Number or Masked Card Number
        /// </summary>
        public string RefundDestination { get; set; }
        /// <summary>
        /// Amount that will be refunded.
        /// </summary>
        public decimal AmountRefunded { get; set; }
        /// <summary>
        /// Pending || Completed || Rejected
        /// </summary>
        public string RefundStatus { get; set; }

        public Payment ToPayment()
        {
            var payment = new Payment()
            {
                OrderId = OrderId,
                AmountAfterCharges = AmountAfterCharges,
                BusinessId = HubtelAccountId,
                AmountPaid = AmountPaid,
                ChargeCustomer = ChargeCustomer,
                Charges = Fee,
                Currency = Currency,
                CustomerMobileNumber = CustomerMobileNumber,
                CustomerName = CustomerName,
                Description = Description,
                ExternalTransactionId = ExternalTransactionId,
                IsSuccessful = IsSuccessful,
                MomoChannel = MobileMoney?.Network,
                MomoPhoneNumber = MobileMoney?.PhoneNumber,
                MomoToken = MobileMoney?.Token,
                PaymentDate = PaymentDate,
                PaymentType = PaymentType,
                ReceiptNumber = ReceiptNumber,
                TransactionId = TransactionId,
                ClientReference = ClientReference,
                ProviderDescription = ProviderDescription,
                ProviderResponseCode = ProviderResponseCode,
                StatusCode = StatusCode,
                FineractSavingsAccountId = FineractSavingsAccountId,
                AmountRefunded = AmountRefunded,
                AmountTendered = AmountTendered,
                IsRecurring = IsRecurring,
                RecurringInvoiceId = RecurringInvoiceId,
                RefundCompletedDate = RefundCompletedDate,
                RefundDestinationType = RefundDestinationType,
                RefundRequestedDate = RefundRequestedDate,
                RefundRequestedBy = RefundRequestedBy,
                RefundDestination = RefundDestination,
                RefundTransactionId = RefundTransactionId,
                RefundStatus = RefundStatus,
                IsRefund = IsRefund
            };
            if (Card != null)
            {
                payment.Tid = Card.Tid;
                payment.Mid = Card.Mid;
                payment.Card = Card.CardNumber;
                payment.CardTransactionId = Card.CardTransactionId;
                payment.Scheme = Card.Scheme;
                payment.Authorization = Card.Authorization;
                payment.CardTransactionMode = Card.TransactionMode;
                payment.CardProcessor = Card.Processor;
                payment.CardIssuer = Card.CardIssuer;
                payment.EntryMode = Card.EntryMode;
            }

            if (Cash != null)
            {
                payment.Balance = Cash.Balance;
                payment.AmountTendered = Cash.AmountTendered;
            }

            if (MobileMoney != null)
            {
                payment.MomoChannel = MobileMoney.Network;
                payment.MomoPhoneNumber = MobileMoney.PhoneNumber;
                payment.MomoToken = MobileMoney.Token;
            }
            payment.CreatedAt = DateTime.UtcNow;

            return payment;
        }
        
    }

    public class Card

    {

        public string CardNumber { get; set; }

        public string Scheme { get; set; }

        public string Tid { get; set; }

        public string Authorization { get; set; }

        public string Mid { get; set; }

        public string CardTransactionId { get; set; }

        public string TransactionMode { get; set; }

        public string Processor { get; set; }
        
        public string CardIssuer { get; set; }
        public string EntryMode { get; set; }

    }
    
    public class MobileMoney
    {
        public string PhoneNumber { get; set; }

        public string Network { get; set; }

        public string Token { get; set; }
    }

    public class Cash
    {
        public decimal AmountTendered { get; set; }

        public decimal Balance { get; set; }
    }
}
