using System.Collections.Generic;
using System.Net.NetworkInformation;

namespace Hubtel.Orders.Consumers.Common.Models
{
    public static class UniqueAppId
    {
        private static string AppId { get; set; }
        public static string GetId()
        {
            if (string.IsNullOrEmpty(AppId))
                AppId = GetDefaultMacAddress();
            return AppId;
        }
        private static string GetDefaultMacAddress()
        {
            Dictionary<string, long> macAddresses = new Dictionary<string, long>();
            foreach (NetworkInterface nic in NetworkInterface.GetAllNetworkInterfaces())
            {
                if (nic.OperationalStatus == OperationalStatus.Up)
                    macAddresses[nic.GetPhysicalAddress().ToString()] = nic.GetIPStatistics().BytesSent + nic.GetIPStatistics().BytesReceived;
            }
            long maxValue = 0;
            string mac = "";
            foreach (KeyValuePair<string, long> pair in macAddresses)
            {
                if (pair.Value > maxValue)
                {
                    mac = pair.Key;
                    maxValue = pair.Value;
                }
            }
            return mac;
        }
    }
}
