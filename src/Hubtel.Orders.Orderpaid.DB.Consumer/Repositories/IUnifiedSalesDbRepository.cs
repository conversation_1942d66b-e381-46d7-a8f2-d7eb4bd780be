using Hubtel.Orders.Orderpaid.PG.Consumer.Models;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Repositories;

public interface IUnifiedSalesDbRepository
{
    Task<bool> AddPaymentEntity(List<PaymentEntity> payment);
    Task<bool> AddInvoiceAdditionEntity(List<InvoiceAdditionEntity> invoiceAdditions);
    Task<bool> AddOrdersEntity(List<OrderEntity> order);
    Task<bool> AddOrderItemsEntity(List<OrderItemsEntity> orderItems);
}