using EFCore.BulkExtensions;
using Hubtel.Orders.Orderpaid.PG.Consumer.Data;
using Hubtel.Orders.Orderpaid.PG.Consumer.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Repositories;

public class UnifiedSalesDbRepository :IUnifiedSalesDbRepository
{
    private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
    private readonly ILogger<UnifiedSalesDbRepository> _logger;

    public UnifiedSalesDbRepository(
        IDbContextFactory<ApplicationDbContext> contextFactory, 
        ILogger<UnifiedSalesDbRepository> logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }

    public async Task<bool> AddPaymentEntity(List<PaymentEntity> payment)
    {
        try
        {
            await using var paymentDb = await _contextFactory.CreateDbContextAsync();
            _logger.LogInformation("payments to commit to db count is: {Orders}", payment.Count);
            await paymentDb.BulkInsertAsync(payment);
            _logger.LogInformation("Payment entity added: {Count}", payment.Count);
            return true;
        }
        catch (Exception e)
        {
            _logger.LogError(e,"an error occurred while persisting to payments table {Message}:{StackTrace}",
                e.Message,e.StackTrace);
            return false;
        }
    }

    public async Task<bool> AddInvoiceAdditionEntity(List<InvoiceAdditionEntity> invoiceAdditions)
    {
        try
        {
            await using var invoiceDb = await _contextFactory.CreateDbContextAsync();
            _logger.LogInformation("invoice to commit to db count is: {Orders}", invoiceAdditions.Count);
            await invoiceDb.BulkInsertAsync(invoiceAdditions);
            _logger.LogInformation("Invoice addition entity added: {Count}",invoiceAdditions.Count);
            return true;
        }
        catch (Exception e)
        {
            _logger.LogError(e,"an error occurred while persisting to invoice additions table {Message}:{StackTrace}",
                e.Message,e.StackTrace);
            return false;
        }
    }

    public async Task<bool> AddOrdersEntity(List<OrderEntity> order)
    {
        try
        {
            await using var orderDb = await _contextFactory.CreateDbContextAsync();
            _logger.LogInformation("orders to commit to db count is: {Orders}", order.Count);
            await orderDb.BulkInsertAsync(order);
            _logger.LogInformation("Order entity added: {Count}", order.Count);
            return true;
        }
        catch (Exception e)
        {
            _logger.LogError(e,"an error occured while persisting to orders table {Message}:{StackTrace}",
                e.Message,e.StackTrace);
            return false;
        }
    }

    public async Task<bool> AddOrderItemsEntity(List<OrderItemsEntity> orderItems)
    {
        try
        {
            await using var orderItemsDb = await _contextFactory.CreateDbContextAsync();
            _logger.LogInformation("order items to commit to db count is: {Orders}", orderItems.Count);
            await orderItemsDb.BulkInsertAsync(orderItems);
            _logger.LogInformation("Order items entity added: {Count}", 
                orderItems.Count);
            return true;
        }
        catch (Exception e)
        {
            _logger.LogError(e,"an error occurred while persisting to orderitems table {Message}: {StackTrace}",
                e.Message,e.StackTrace);
            return false;
        }
    }
}