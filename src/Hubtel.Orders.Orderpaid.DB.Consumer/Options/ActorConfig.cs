namespace Hubtel.Orders.Orderpaid.PG.Consumer.Options;

public abstract class BaseActorConfig
{
    public int NumberOfInstances { get; set; } = 10;
    public int UpperBound { get; set; } = 100;
}

public class ActorConfig
{
    public DbActorConfig DbActorConfig { get; set; } = new();
    public InvoiceAdditionEntityActorConfig InvoiceAdditionEntityActorConfig { get; set; } = new();
    public OrderEntityActorConfig OrderEntityActorConfig { get; set; } = new();
    public PaymentEntityActorConfig PaymentEntityActorConfig { get; set; } = new();
    public OrderItemEntityActorConfig OrderItemEntityActorConfig { get; set; } = new();
}

public class DbActorConfig : BaseActorConfig
{
}
public class InvoiceAdditionEntityActorConfig : BaseActorConfig
{
}
public class OrderEntityActorConfig : BaseActorConfig
{
}
public class PaymentEntityActorConfig : BaseActorConfig
{
}
public class OrderItemEntityActorConfig : BaseActorConfig
{
}
