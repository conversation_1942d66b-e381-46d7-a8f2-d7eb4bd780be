using Hubtel.Orders.Orderpaid.PG.Consumer.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Data.Configurations;

public class InvoiceAdditionsConfiguration: IEntityTypeConfiguration<InvoiceAdditionEntity>
{
    public void Configure(EntityTypeBuilder<InvoiceAdditionEntity> builder)
    {
        builder.HasKey(o => o.Id);
        builder.Property(o => o.Id).ValueGeneratedNever();
        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");
        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");
    }
}