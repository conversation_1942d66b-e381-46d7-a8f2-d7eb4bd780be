using Hubtel.Orders.Orderpaid.PG.Consumer.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Data.Configurations;

public class OrdersConfiguration : IEntityTypeConfiguration<OrderEntity>
{
    public void Configure(EntityTypeBuilder<OrderEntity> builder)
    {
        builder.HasKey(o => o.Id);
        builder.Property(o => o.Id).ValueGeneratedNever();
        builder.Property(c => c.CreatedAt).HasColumnType("timestamp");
        builder.Property(c => c.UpdatedAt).HasColumnType("timestamp");
    }
}