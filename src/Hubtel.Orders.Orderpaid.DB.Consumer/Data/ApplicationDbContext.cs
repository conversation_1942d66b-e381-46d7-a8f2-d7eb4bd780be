using System.Reflection;
using Hubtel.Orders.Orderpaid.PG.Consumer.Models;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Data;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) 
        : base(options)
    {
        AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
        AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
    }

    public DbSet<OrderEntity> Orders => Set<OrderEntity>();
    public DbSet<PaymentEntity> Payments => Set<PaymentEntity>();
    public DbSet<InvoiceAdditionEntity> OrderInvoiceAdditions => Set<InvoiceAdditionEntity>();
    public DbSet<OrderItemsEntity> OrderItems => Set<OrderItemsEntity>();

    protected override void OnModelCreating(ModelBuilder builder)
    {
        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(builder);
    }
}