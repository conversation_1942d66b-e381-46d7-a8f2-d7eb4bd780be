<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>Hubtel.Orders.Orderpaid.PG.Consumer</RootNamespace>
    </PropertyGroup>


    <ItemGroup>
        <PackageReference Include="Akka.Hosting" Version="1.5.22"/>
        <PackageReference Include="Confluent.Kafka" Version="2.4.0"/>
        <PackageReference Include="Confluent.Kafka.Extensions.Diagnostics" Version="0.4.0"/>
        <PackageReference Include="EFCore.BulkExtensions.PostgreSql" Version="7.0.0" />
        <PackageReference Include="Hubtel.Kafka.Producer.Sdk" Version="8.0.5" />
        <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0"/>
        <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2"/>
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0"/>
        
        <PackageReference Include="Hubtel.Instrumentation" Version="8.0.4"/>
        <PackageReference Include="Hubtel.Kafka.Host" Version="8.0.5"/>

        <PackageReference Include="Akka" Version="1.5.24"/>
        <PackageReference Include="Akka.DependencyInjection" Version="1.5.24"/>

        <PackageReference Include="Mapster" Version="7.4.0"/>
        <PackageReference Include="JustEat.StatsD" Version="5.0.1"/>
        <PackageReference Include="Gelf.Extensions.Logging" Version="2.6.0"/>
        <PackageReference Include="Flurl.Http" Version="4.0.2"/>
        <PackageReference Include="StackExchange.Redis" Version="2.7.33"/>
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>

        <PackageReference Include="Npgsql" Version="8.0.3"/>
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4"/>
        <PackageReference Include="Npgsql.Json.NET" Version="8.0.3"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.6"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.6">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.6">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
        </PackageReference>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.6.0"/>
    </ItemGroup>


    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="README.md">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>
