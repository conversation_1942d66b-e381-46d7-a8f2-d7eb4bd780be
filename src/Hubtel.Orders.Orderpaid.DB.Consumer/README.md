
# Getting Started with Hubtel API PG Template

Instructions:
* change database name in DbConnection in appsettings.json
* create migrations for your models using this command:

`   dotnet ef migrations add "Init_Migration" -o ./Data/Migrations   `
####Note that Customer model is only for test purposes. You may delete it!!

* run this command to complete the migration process:

`dotnet ef database update`

####A "Migrations" folder will be added to your project.