using Hubtel.Orders.Orderpaid.PG.Consumer.Actors.Messages;
using Hubtel.Orders.Orderpaid.PG.Consumer.Services;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Actors;

public class DbActor:BaseActor
{
    public DbActor(IDbPersistingService dbPersistingService)
    {
        ReceiveAsync<PersistToDbMessage>(async m=> 
            await dbPersistingService.DoDbPersistOperations(m.Orders));
        // Retry Payment Persist Operations
        ReceiveAsync<PersistPaymentMessage>(async m=> 
            await dbPersistingService.DoRetryPaymentPersistOperations(m.Orders));
        // Persist Order Item Persist Operations
        ReceiveAsync<PersistOrderItemMessage>(async m=> 
            await dbPersistingService.DoRetryOrderItemPersistOperations(m.Orders));
        // Retry Invoice Persist Operations
        ReceiveAsync<PersistInvoiceAdditionMessage>(async m=> 
            await dbPersistingService.DoRetryInvoiceAdditionPersistOperations(m.Orders));
        
    }
}

