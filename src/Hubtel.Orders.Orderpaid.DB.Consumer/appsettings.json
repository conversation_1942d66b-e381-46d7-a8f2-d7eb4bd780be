{"ApplicationInsights": {"InstrumentationKey": "xxx"}, "ConnectionStrings": {"DbConnection": "Server=127.0.0.1;Port=5432;User Id=********;Password=********;Database=HubtelUnifiedSales;Pooling=true;CommandTimeout=120;Timeout=30"}, "KafkaConsumerConfig": {"BootstrapServers": "localhost:9092", "GroupId": "paymentStateReceived.groupId", "Topics": ["hubtel.sales.order_payment_state_received"], "ExtraProperties": {"auto.offset.reset": "latest"}}, "Logging": {"LogLevel": {"Default": "Information", "System": "Information", "Microsoft": "Information"}, "GELF": {"Host": "localhost", "Port": 12202, "LogSource": "Hubtel.Orders.Orderpaid.PG.Consumer", "Facility": "Hubtel.Orders.Orderpaid.PG.Consumer", "Environment": "Production", "LogLevel": {"Default": "Information"}}}, "MessageGroupConsumerLogicConfig": {"TimeoutInMilliseconds": 5000, "MaxElements": 200}, "StatsdConfig": {"Server": "localhost", "Port": 8125, "Prefix": "statsd_prefix_here"}, "ActorConfig": {"DbActorConfig": {"NumberOfInstances": 2, "UpperBound": 1000}}, "CardVerificationConfig": {"BusinessId": "NHcc0b10e9f2c84026806bb5f30031084b"}, "KafkaProducerConfig": {"Hosts": [{"Alias": "default", "BootstrapServers": "127.0.0.1:9092"}]}, "KafkaExtra": {"OrdersRetryTopic": "hubtel.sales.orders.retry", "PaymentRetryTopic": "hubtel.sales.payment.retry", "OrdersItemsRetryTopic": "hubtel.sales.orders.items.retry", "InvoiceAdditionRetryTopic": "hubtel.sales.invoice.addition.retry"}}