import json
from kafka import KafkaProducer
producer = KafkaProducer(bootstrap_servers=['127.0.0.1:9092'], api_version=(0, 10), value_serializer=lambda m: json.dumps(m).encode('ascii'))
# produce asynchronously
i = 0
while i < 1:
  producer.send('hubtel.sales.order_payment_state_received',value =
  {
      "Id": "31247f33374b4e13aaffe42d2fe6fb22",
      "IsEcommerceOrder": False,
      "CreatedAt": "2025-08-26T14:25:59.5875261Z",
      "UpdatedAt": None,
      "CreatedBy": None,
      "UpdatedBy": None,
      "IntegrationChannel": None,
      "PosDeviceId": None,
      "BusinessId": "NH01eaeb3a02a9458da201a186d985c251",
      "PosDeviceType": None,
      "OrderDate": "2025-08-26T14:25:59.5875261Z",
      "OrderNumber": "68618fbaf40a4f50966945c26846f7a2",
      "Note": "SP005372",
      "Description": "SP005372",
      "Status": "Paid",
      "AssignedTo": None,
      "EmployeeId": "233244666396",
      "EmployeeName": "",
      "CustomerMobileNumber": "************",
      "CustomerName": "SP005372",
      "BranchId": "1a1e105d319e4164be673271a8ee69af",
      "BranchName": "Kumasi",
      "TaxRate": None,
      "TaxAmount": None,
      "DiscountRate": None,
      "DiscountAmount": None,
      "Subtotal": 50.0,
      "TotalAmountDue": 50.5,
      "AmountPaid": 50.5,
      "AmountRefunded": 0.0,
      "PaymentTypes": None,
      "Location": None,
      "Currency": "GHS",
      "OrderItems": [
          {
              "ItemId": "61853e7542874276af22991ae13563f0",
              "OrderId": "31247f33374b4e13aaffe42d2fe6fb22",
              "Name": "Scan Plan Policy",
              "Quantity": 1,
              "UnitPrice": 50.0,
              "ServiceRequestId": None,
              "ServiceData": None
          }
      ],
      "InvoiceAdditions": [],
      "IsFulfilled": None,
      "ConsumerRating": 0,
      "ConsumerFeedback": None,
      "CustomerEmail": None,
      "BusinessEmail": "<EMAIL>",
      "BusinessMobileNumber": "233244666396",
      "BusinessName": "Spectra Health Mutual Insurance Scheme",
      "FcmCustomer": None,
      "FcmDevice": None,
      "AmountDueProducer": 0.0,
      "DeliveryFee": 0.0,
      "HasDelivery": False,
      "CustomerReward": 0.0,
      "SenderId": "SHMIS",
      "LogoUrl": "https://drwfqcqcb8ho1.cloudfront.net/images/05b407609e3b4828818dafcd9d82385e-18012023155017.jpeg",
      "ReturnUrl": None,
      "BranchEmail": None,
      "BranchPhoneNumber": None,
      "CancellationUrl": None,
      "DeliveryLocationName": None,
      "PaymentRequest": {
          "Channel": "mtn_gh_rec",
          "CustomerMsisdn": "************",
          "PrimaryCallbackUrl": None,
          "Token": None,
          "PaymentType": "MobileMoney",
          "FeesOnCustomer": True,
          "BusinessId": "NH01eaeb3a02a9458da201a186d985c251",
          "Amount": 50.5,
          "Description": "SP005372",
          "ClientReference": "dbc92260369740d4b680d42aabe7ea8b",
          "OrderId": "31247f33374b4e13aaffe42d2fe6fb22",
          "Currency": "GHS",
          "Fee": 0.5,
          "PaymentCategory": None,
          "Tips": 0.0,
          "AmountPlusTips": 0.0,
          "TipSettlement": 0.0,
          "HasTip": False,
          "DeliveryFee": 0.0,
          "SavingsAccountId": 2017260,
          "AmountAfterCharges": 50.0,
          "BusinessName": "Spectra Health Mutual Insurance Scheme",
          "ReceiptNumber": "beea895ec97648a2ab913cce96f5f56c",
          "HubtelReference": "198E6C57F23521580331",
          "AmountTendered": 0.0,
          "CustomerMobileNumber": "************",
          "TransactionId": None,
          "CustomerName": None,
          "IsRefund": False,
          "Balance": 0.0,
          "OrderNumber": "68618fbaf40a4f50966945c26846f7a2",
          "OrderDate": "0001-01-01T00:00:00",
          "IsRecurring": True,
          "RecurringInvoiceId": "6b0e7516fdd6415db771f108b20224ad",
          "RefundTransactionId": None,
          "RefundRequestedDate": None,
          "RefundCompletedDate": None,
          "RefundRequestedBy": None,
          "RefundDestinationType": None,
          "RefundDestination": None,
          "AmountRefunded": 0.0,
          "RefundStatus": None,
          "PaymentProcessorRoute": None,
          "PaymentProcessorName": None
      },
      "IsRecurring": True,
      "RecurringInvoiceId": "6b0e7516fdd6415db771f108b20224ad",
      "IsProgrammableService": False,
      "TotalProfit": 0.0,
      "Payment": {
          "Id": "106f98abd6554f78901a3567191b5147",
          "BusinessId": "NH01eaeb3a02a9458da201a186d985c251",
          "CreatedAt": "2025-08-26T14:26:07.1221144Z",
          "UpdatedAt": None,
          "CreatedBy": None,
          "UpdatedBy": None,
          "PaymentType": "mobilemoney",
          "OrderId": "31247f33374b4e13aaffe42d2fe6fb22",
          "MomoPhoneNumber": "************",
          "MomoChannel": "mtn_gh_rec",
          "MomoToken": None,
          "TransactionId": "198E6C57F23521580331",
          "ExternalTransactionId": "***********",
          "AmountAfterCharges": 50.0,
          "Charges": 0.5,
          "PaymentCategory": None,
          "Tips": 0.0,
          "AmountPlusTips": 0.0,
          "TipSettlement": 0.0,
          "HasTip": False,
          "ChargeCustomer": True,
          "AmountPaid": 50.5,
          "ElevyAmount": 0.0,
          "PaymentDate": "2025-08-26T14:25:59.5875261Z",
          "Note": None,
          "Description": "SP005372",
          "PosDeviceId": None,
          "PosDeviceType": None,
          "EmployeeId": "233244666396",
          "EmployeeName": "",
          "CustomerMobileNumber": "************",
          "CustomerName": None,
          "BranchId": "1a1e105d319e4164be673271a8ee69af",
          "BranchName": "Kumasi",
          "IsRefund": False,
          "IsSuccessful": True,
          "ReceiptNumber": "beea895ec97648a2ab913cce96f5f56c",
          "Location": None,
          "Currency": "GHS",
          "Scheme": None,
          "Card": None,
          "Tid": None,
          "Authorization": None,
          "Mid": None,
          "CardTransactionId": None,
          "AmountTendered": 0.0,
          "Balance": 0.0,
          "ClientReference": "dbc92260369740d4b680d42aabe7ea8b",
          "ProviderDescription": "The MTN Mobile Money payment has been approved and processed successfully.",
          "ProviderResponseCode": "SUCCESSFUL",
          "StatusCode": "0000",
          "FineractSavingsAccountId": 2017260,
          "CardTransactionMode": None,
          "CardProcessor": None,
          "CallbackUrl": None,
          "IsRecurring": True,
          "RecurringInvoiceId": "6b0e7516fdd6415db771f108b20224ad",
          "RefundTransactionId": None,
          "RefundRequestedDate": None,
          "RefundCompletedDate": None,
          "RefundRequestedBy": None,
          "RefundDestinationType": None,
          "RefundDestination": None,
          "AmountRefunded": 0.0,
          "RefundStatus": None,
          "DeliveryFee": 0.0,
          "PaymentProcessor": None,
          "CardIssuer": None,
          "IntegrationChannel": None,
          "EntryMode": None,
          "TaxWithHolding": 0.0,
          "IsBulkSettlement": True
      },
      "IsMultiCart": False,
      "IsMultiCartitem": False,
      "IsConsumerSettlement": False,
      "SalesDeviceId": None,
      "SalesDeviceType": None,
      "ConsumerSettlementNumber": None,
      "PaylinkUrl": None,
      "PaylinkId": None,
      "ScenarioType": None,
      "PaylinkTitle": None,
      "PaylinkDescription": None,
      "AggregatedAmount": 0.0,
      "RemainingAmount": 0.0,
      "AmountRequested": 0.0,
      "IsBusiness": False,
      "PaylinkUniqueId": None,
      "MessageId": None,
      "IsPartialPay": False,
      "PayLinkSourceType": None,
      "NeedsGateKeeperCheck": False,
      "HasGateKeeper": False,
      "IsBlacklistedConsumer": False,
      "WalletId": 0,
      "PaylinkCallbackUrl": None,
      "BNPLDetails": None,
      "Country": None,
      "Region": None,
      "City": None,
      "Zone": None,
      "Station": None,
      "Longitude": None,
      "Latitude": None,
      "InvoiceId": "",
      "IsFirstPaymentInvoice": True,
      "IsELevyOrder": False,
      "DestinationData": None,
      "IsCardToBank": False,
      "CardVerification": None,
      "InstantServiceDetails": None,
      "ElevyDetails": None,
      "Kyc": None,
      "IsDirectDebitOrder": False,
      "PartnerBusinessId": None,
      "PartnerBranchId": None,
      "MultiCartIds": [],
      "Coupons": [],
      "IsPrepaidTransferEnabled": False,
      "IsBulkSettlement": True,
      "PrepaidSettlementAccount": None,
      "TaxWithHolding": 0.0
  },
  )
  i = i + 1
  print("produced")
  print("hubtel.sales.order_payment_state_received")
producer.flush()