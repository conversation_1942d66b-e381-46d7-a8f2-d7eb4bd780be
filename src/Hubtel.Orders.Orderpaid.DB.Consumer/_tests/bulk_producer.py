import json
import uuid
import random
from kafka import KafkaProducer

# Random pool of business names
business_names = [
    "QuickShop", "MegaMart", "Hubtel Store", "FreshMart",
    "PayGo", "EasyTopup", "Foodies", "SmartPay",
    "EcoShop", "FastBuy", None  # Include None to produce null values
]

producer = KafkaProducer(
    bootstrap_servers=['127.0.0.1:9092'],
    api_version=(0, 10),
    value_serializer=lambda m: json.dumps(m).encode('ascii')
)

for i in range(50):
    id = str(uuid.uuid4())
    order_number = id

    # Randomize business name or set None
    business_name = None if i % 2 == 0 else random.choice(business_names)

    msg =  {
        "Id": id,
        "IsEcommerceOrder": False,
        "CreatedAt": "2024-11-25T09:06:36.0485626+00:00",
        "UpdatedAt": None,
        "CreatedBy": None,
        "UpdatedBy": None,
        "IntegrationChannel": "USSD Checkout",
        "PosDeviceId": None,
        "BusinessId": "ae549a0bbd9e453fa3aa19a206b6cbc3",
        "PosDeviceType": None,
        "OrderDate": "2024-11-25T09:06:36.0485626+00:00",
        "OrderNumber": order_number,
        "Note": "SportyBet Topup",
        "Description": "SportyBet Topup",
        "Status": "Paid",
        "AssignedTo": None,
        "EmployeeId": "0540134222",
        "EmployeeName": "",
        "CustomerMobileNumber": "************",
        "CustomerName": "MUSAH DAWUDA",
        "BranchId": "dd3f5b9a233b41cb815e2817d535a437",
        "BranchName": "Main Branch",
        "TaxRate": None,
        "TaxAmount": None,
        "DiscountRate": None,
        "DiscountAmount": None,
        "Subtotal": 10.0,
        "TotalAmountDue": 10.0,
        "AmountPaid": 10.0,
        "AmountRefunded": 0.0,
        "PaymentTypes": None,
        "Location": "",
        "Currency": "GHS",
        "OrderItems": [
            {
                "ItemId": "22306c0c09664a44b7eff4ddf36b746c",
                "OrderId": id,
                "Name": "SportyBet Topup",
                "Quantity": 1,
                "UnitPrice": 10.0,
                "ServiceRequestId": "2b408b72ec1f442eafd212951fd56e25",
                "ServiceData": None
            }
        ],
        "InvoiceAdditions": [],
        "IsFulfilled": None,
        "ConsumerRating": 0,
        "ConsumerFeedback": None,
        "CustomerEmail": None,
        "BusinessEmail": "<EMAIL>",
        "BusinessMobileNumber": "0540134222",
        "BusinessName": business_name,
        "FcmCustomer": None,
        "FcmDevice": None,
        "AmountDueProducer": 0.0,
        "DeliveryFee": 0.0,
        "HasDelivery": False,
        "CustomerReward": 0.0,
        "SenderId": "SportyBet",
        "LogoUrl": "https://drwfqcqcb8ho1.cloudfront.net/images/41ed85583dd74daf906ae1bf556fd170-05102018123124.png",
        "ReturnUrl": None,
        "BranchEmail": "",
        "BranchPhoneNumber": "",
        "CancellationUrl": None,
        "DeliveryLocationName": None,
        "PaymentRequest": {
            "Channel": "mtn-gh",
            "CustomerMsisdn": "************",
            "PrimaryCallbackUrl": None,
            "Token": None,
            "PaymentType": "MOMO",
            "FeesOnCustomer": False,
            "BusinessId": "ae549a0bbd9e453fa3aa19a206b6cbc3",
            "Amount": 10.0,
            "Description": "SportyBet Topup",
            "ClientReference": "2b408b72ec1f442eafd212951fd56e25",
            "OrderId": id,
            "Currency": "GHS",
            "Fee": 0.2,
            "Tips": 0.0,
            "AmountPlusTips": 0.0,
            "TipSettlement": 0.0,
            "HasTip": False,
            "DeliveryFee": 0.0,
            "SavingsAccountId": 11673,
            "AmountAfterCharges": 9.8,
            "BusinessName": business_name,
            "ReceiptNumber": "2b408b72ec1f442eafd212951fd56e25",
            "HubtelReference": "1936292A953235772791",
            "AmountTendered": 0.0,
            "CustomerMobileNumber": None,
            "TransactionId": None,
            "CustomerName": None,
            "IsRefund": False,
            "Balance": 0.0,
            "OrderNumber": "2b408b72ec1f442eafd212951fd56e25",
            "OrderDate": "0001-01-01T00:00:00",
            "IsRecurring": False,
            "RecurringInvoiceId": None,
            "RefundTransactionId": None,
            "RefundRequestedDate": None,
            "RefundCompletedDate": None,
            "RefundRequestedBy": None,
            "RefundDestinationType": None,
            "RefundDestination": None,
            "AmountRefunded": 0.0,
            "RefundStatus": None,
            "PaymentProcessorRoute": "hubtel.payments.receivemoney.mtn_gh.default_3",
            "PaymentProcessorName": "MTN GH Receive Money - Default-3"
        },
        "IsRecurring": False,
        "RecurringInvoiceId": None,
        "IsProgrammableService": True,
        "TotalProfit": 0.0,
        "Payment": {
            "Id": id,
            "BusinessId": "ae549a0bbd9e453fa3aa19a206b6cbc3",
            "CreatedAt": "2024-11-25T09:06:54.0781179Z",
            "UpdatedAt": None,
            "CreatedBy": None,
            "UpdatedBy": None,
            "PaymentType": "mobilemoney",
            "OrderId": id,
            "MomoPhoneNumber": "************",
            "MomoChannel": "mtn-gh",
            "MomoToken": None,
            "TransactionId": "1936292A953235772791",
            "ExternalTransactionId": "***********",
            "AmountAfterCharges": 9.8,
            "Charges": 0.2,
            "Tips": 0.0,
            "AmountPlusTips": 0.0,
            "TipSettlement": 0.0,
            "HasTip": False,
            "ChargeCustomer": False,
            "AmountPaid": 10.0,
            "ElevyAmount": 0.0,
            "PaymentDate": "2024-11-25T09:06:36.0485626+00:00",
            "Note": None,
            "Description": "SportyBet Topup",
            "PosDeviceId": None,
            "PosDeviceType": None,
            "EmployeeId": "0540134222",
            "EmployeeName": "",
            "CustomerMobileNumber": "************",
            "CustomerName": None,
            "BranchId": "dd3f5b9a233b41cb815e2817d535a437",
            "BranchName": "Main Branch",
            "IsRefund": False,
            "IsSuccessful": True,
            "ReceiptNumber": "2b408b72ec1f442eafd212951fd56e25",
            "Location": "",
            "Currency": "GHS",
            "Scheme": None,
            "Card": None,
            "Tid": None,
            "Authorization": None,
            "Mid": None,
            "CardTransactionId": None,
            "AmountTendered": 0.0,
            "Balance": 0.0,
            "ClientReference": "2b408b72ec1f442eafd212951fd56e25",
            "ProviderDescription": "The MTN Mobile Money payment has been approved and processed successfully.",
            "ProviderResponseCode": "SUCCESSFUL",
            "StatusCode": "0000",
            "FineractSavingsAccountId": 11673,
            "CardTransactionMode": None,
            "CardProcessor": None,
            "CallbackUrl": None,
            "IsRecurring": False,
            "RecurringInvoiceId": None,
            "RefundTransactionId": None,
            "RefundRequestedDate": None,
            "RefundCompletedDate": None,
            "RefundRequestedBy": None,
            "RefundDestinationType": None,
            "RefundDestination": None,
            "AmountRefunded": 0.0,
            "RefundStatus": None,
            "DeliveryFee": 0.0,
            "PaymentProcessor": "MTN GH Receive Money - Default-3",
            "CardIssuer": None,
            "IntegrationChannel": "USSD Checkout",
            "EntryMode": None
        },
        "IsMultiCart": False,
        "IsMultiCartitem": False,
        "IsConsumerSettlement": False,
        "SalesDeviceId": None,
        "SalesDeviceType": None,
        "ConsumerSettlementNumber": None,
        "PaylinkUrl": None,
        "PaylinkId": None,
        "ScenarioType": None,
        "PaylinkTitle": None,
        "PaylinkDescription": None,
        "AggregatedAmount": 0.0,
        "RemainingAmount": 0.0,
        "AmountRequested": 0.0,
        "IsBusiness": False,
        "PaylinkUniqueId": None,
        "MessageId": None,
        "IsPartialPay": False,
        "PayLinkSourceType": None,
        "NeedsGateKeeperCheck": False,
        "HasGateKeeper": False,
        "IsBlacklistedConsumer": False,
        "WalletId": 0,
        "PaylinkCallbackUrl": None,
        "BNPLDetails": None,
        "Country": None,
        "Region": None,
        "City": None,
        "Zone": None,
        "Station": None,
        "Longitude": None,
        "Latitude": None,
        "InvoiceId": None,
        "IsFirstPaymentInvoice": False,
        "IsELevyOrder": False,
        "DestinationData": None,
        "IsCardToBank": False,
        "CardVerification": None,
        "InstantServiceDetails": None,
        "ElevyDetails": None,
        "Kyc": None,
        "IsDirectDebitOrder": False,
        "PartnerBusinessId": None,
        "PartnerBranchId": None,
        "MultiCartIds": [],
        "Coupons": []
    }

    producer.send('hubtel.sales.order_payment_state_received', value=msg)
    print(f"Produced message {i+1} with BusinessName={business_name}")

producer.flush()
