namespace Hubtel.Orders.Orderpaid.PG.Consumer.Models;

public class OrderItemsEntity
{
    public string? Id { get; set; } = Guid.NewGuid().ToString("N");


    public string? BusinessId { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    public string? CreatedBy { get; set; }

    public string? UpdatedBy { get; set; }
    public string? ItemId { get; set; }

    public string? OrderId { get; set; }


    public string? Name { get; set; }

    public string? VariantName { get; set; }

    public int Quantity { get; set; }

    public decimal UnitPrice { get; set; }

    public float? DiscountRate { get; set; }

    public decimal? DiscountAmount { get; set; }

    public string? Note { get; set; }

    public bool IsReturned { get; set; }

    public string? ServiceRequestId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? ServiceData { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public decimal UnitProfit { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public decimal Profit { get; set; }
}