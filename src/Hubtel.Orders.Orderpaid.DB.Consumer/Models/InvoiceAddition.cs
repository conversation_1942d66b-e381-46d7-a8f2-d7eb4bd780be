namespace Hubtel.Orders.Orderpaid.PG.Consumer.Models;

public class InvoiceAddition
{
    public string? Id { get; set; }
    public string? BusinessId { get; set; }
    public string? OrderId { get; set; }
    public string? BranchId { get; set; }
    public bool IsInclusive { get; set; }
    public short Sequence { get; set; }
    public string? Name { get; set; }
    public bool IsFlatFee { get; set; }
    public decimal Figure { get; set; }
    public string? CalculationMethod { get; set; }
    public decimal ComputedValue { get; set; }
    public bool IsActive { get; set; }
}