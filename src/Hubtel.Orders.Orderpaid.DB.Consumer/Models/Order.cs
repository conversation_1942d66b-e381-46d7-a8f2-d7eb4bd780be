using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using Akka.Streams;
using Newtonsoft.Json;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Models;

public class Order
{
    public string? Id { get; set; }

    public bool IsEcommerceOrder { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public string? CreatedBy { get; set; }

    public string? UpdatedBy { get; set; }
    public string? IntegrationChannel { get; set; }

    public string? PosDeviceId { get; set; }
    public string? BusinessId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? PosDeviceType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public DateTime OrderDate { get; set; }
    [StringLength(50)]
    public string? OrderNumber { get; set; }
    [OnDeserialized]
    private void OnDeserializedMethod(StreamingContext context)
    {
        if (OrderNumber?.Length > 50)
        {
            OrderNumber = OrderNumber[..50];
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public string? Note { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? Description { get; set; }

    public string? Status { get; set; } //PAID, PARITALLY PAID, UNPAID, CANCELLED

    /// <summary>
    /// 
    /// </summary>
    public string? AssignedTo { get; set; }

    public string? EmployeeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? EmployeeName { get; set; }

    public string? CustomerMobileNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? CustomerName { get; set; }

    public string? BranchId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? BranchName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public float? TaxRate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public decimal? TaxAmount { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public float? DiscountRate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public decimal? DiscountAmount { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public decimal Subtotal { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public decimal TotalAmountDue { get; set; }

    /// <summary>
    /// This is a total of all payments
    /// </summary>
    public decimal AmountPaid { get; set; }

    /// <summary>
    /// This is a total of all refunds
    /// </summary>
    public decimal AmountRefunded { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? PaymentTypes { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? Currency { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();

    /// <summary>
    /// 
    /// </summary>
    ///
    [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
    public ICollection<InvoiceAddition> InvoiceAdditions { get; set; } = new List<InvoiceAddition>();

    /// <summary>
    /// 
    /// </summary>
    /// <summary>
    /// 
    /// </summary>
    public bool? IsFulfilled { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int ConsumerRating { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? ConsumerFeedback { get; set; }

    public string? CustomerEmail { get; set; }

    public string? BusinessEmail { get; set; }

    public string? BusinessMobileNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? BusinessName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? FcmCustomer { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? FcmDevice { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public decimal AmountDueProducer { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public decimal DeliveryFee { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool HasDelivery { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double CustomerReward { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? SenderId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? LogoUrl { get; set; }

    /// <summary>
    /// 
    /// </summary>

    public string? ReturnUrl { get; set; }

    /// <summary>
    /// 
    /// </summary>
    /// [string?Length(40)]
    public string? BranchEmail { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? BranchPhoneNumber { get; set; }

    public string? CancellationUrl { get; set; }

    /// <summary>
    /// Human readable location of a delivery, e.g. Kokomlemle
    /// </summary>
    public string? DeliveryLocationName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public PaymentRequest? PaymentRequest { get; set; }

    /// <summary>
    /// Indicates whether the invoice was created due to a recurring payment
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// RecurringInvoiceID
    /// </summary>
    public string? RecurringInvoiceId { get; set; }

    /// <summary>
    /// Determines whether order is for a service so that certain services will be rendered
    /// </summary>
    public bool IsProgrammableService { get; set; }

    /// <summary>
    /// Total profit made on the sale
    /// </summary>
    public decimal TotalProfit { get; set; }

    public Payment? Payment { get; set; } //will be NULL during deserializing but will be populated after
    public bool IsMultiCart { get; set; }
    public bool IsMultiCartitem { get; set; }
    public bool IsConsumerSettlement { get; set; }
    public string? SalesDeviceId { get; set; }
    public string? SalesDeviceType { get; set; }
    public string? ConsumerSettlementNumber { get; set; }
    public string? PaylinkUrl { get; set; }
    public string? PaylinkId { get; set; }
    public string? ScenarioType { get; set; }
    public string? PaylinkTitle { get; set; }
    public string? PaylinkDescription { get; set; }
    public decimal AggregatedAmount { get; set; }
    public decimal RemainingAmount { get; set; }
    public decimal AmountRequested { get; set; }
    public bool IsBusiness { get; set; }

    public string? PaylinkUniqueId { get; set; }
    public string? MessageId { get; set; }
    public bool IsPartialPay { get; set; }

    public string? PayLinkSourceType { get; set; }

    public bool NeedsGateKeeperCheck { get; set; }
    public bool HasGateKeeper { get; set; }
    public bool IsBlacklistedConsumer { get; set; }
    public int WalletId { get; set; }
    public string? PaylinkCallbackUrl { get; set; }


    public BNPLDetails? BNPLDetails { get; set; }
    public string? Country { get; set; }

    public string? Region { get; set; }

    public string? City { get; set; }

    public string? Zone { get; set; }

    public string? Station { get; set; }

    public string? Longitude { get; set; }
    public string? Latitude { get; set; }
    public string? InvoiceId { get; set; }
    public bool IsFirstPaymentInvoice { get; set; }
    public bool IsELevyOrder { get; set; }
    public string? DestinationData { get; set; }
    public bool IsCardToBank { get; set; }
    public CardVerificationDto? CardVerification { get; set; }
    public InstantServiceDetails? InstantServiceDetails { get; set; }
    public ElevyDetails? ElevyDetails { get; set; }
    public string? Kyc { get; set; }
    public bool IsDirectDebitOrder { get; set; }
    public string? PartnerBusinessId { get; set; }
    public string? PartnerBranchId { get; set; }
    public List<MultiCartItem?>? MultiCartIds { get; set; }
    public List<Coupon> Coupons { get; set; } = new();
}

public class InstantServiceDetails
{
    public string? Destination { get; set; }
    public string? RecipientName { get; set; }
    public string? Reference { get; set; }
    public string? ElevyAmount { get; set; }
    public Reason? Reason { get; set; }
}

public class Reason
{
    public string? Id { get; set; }
    public string? ImageUrl { get; set; }
    public string? Name { get; set; }
}

public class CardVerificationDto
{
    public string? CardVerificationRequestId { get; set; }
    public bool IsCardVerificationOrder { get; set; }
    public string? OrderType { get; set; }
}

public class ElevyDetails
{
    public string? ELevyId { get; set; }
    public decimal ElevyAmount { get; set; }
    public decimal TaxableAmount { get; set; }
    public decimal TransferAmount { get; set; }
    public string? Source { get; set; }
    public string? SenderTin { get; set; }
    public string? ReceiverAccountNumber { get; set; }
    public string? SenderAccountNumber { get; set; }
    public bool IsCardToBank { get; set; }
    public string? ReceiverInstitutionId { get; set; }
    public string? SenderIssuerId { get; set; }
    public string? ClientTransactionId { get; set; }
}

public class BNPLDetails
{
    public bool IsBnplOrder { get; set; }
    public decimal BnplBalance { get; set; }
    public string? BnplOrderDescription { get; set; }
    public string? WalletProvider { get; set; }
}

public class MultiCartItem
{
    public string? Value { get; set; }
}

public class Coupon
{
    public string? Type { get; set; }
    public string? SubType { get; set; }
    public float Unit { get; set; }

    public string? CreatedBy { get; set; }

    public DateTime CreatedOn { get; set; }
    public DateTime? RedeemedOn { get; set; }
    public DateTime? OnHoldSince { get; set; }
    public DateTime? AcceptedOn { get; set; }
    public DateTime? DeletedOn { get; set; }

    public CouponState State { get; set; }

    public bool Deleted { get; set; }
    public string? Id { get; set; }
    public string? OrderId { get; set; }

    public long GetCouponIdAsLong()
    {
        if (!long.TryParse(Id, out var id)) return 0L;

        return id;
    }
}

public enum CouponState
{
    New,
    Accepted,

    Used,
    OnHold
}