namespace Hubtel.Orders.Orderpaid.PG.Consumer.Models;

public class PaymentRequest
{
    public string? Channel { get; set; }
    public string? CustomerMsisdn { get; set; }
    public string? PrimaryCallbackUrl { get; set; }
    public string? Token { get; set; }
    public string? PaymentType { get; set; }
    public bool FeesOnCustomer { get; set; }
    public string? BusinessId { get; set; }
    public decimal Amount { get; set; }
    public string? Description { get; set; }
    public string? ClientReference { get; set; }
    public string? OrderId { get; set; }
    public string? Currency { get; set; }
    public decimal Fee { get; set; }
    public string? PaymentCategory { get; set; }
    public decimal Tips { get; set; }
    public decimal AmountPlusTips { get; set; }
    public decimal TipSettlement { get; set; }
    public bool HasTip { get; set; }
    public decimal DeliveryFee { get; set; }
    public int SavingsAccountId { get; set; }
    public decimal AmountAfterCharges { get; set; }
    public string? BusinessName { get; set; }
    public string? ReceiptNumber { get; set; }
    public string? HubtelReference { get; set; }
    public decimal AmountTendered { get; set; }
    public string? CustomerMobileNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? TransactionId { get; set; } // from merchant account

    public string? CustomerName { get; set; }
    public bool IsRefund { get; set; }
    public decimal Balance { get; set; }
    public string? OrderNumber { get; set; }
    public DateTime OrderDate { get; set; }

    /// <summary>
    /// Indicates whether the invoice was created due to a recurring payment
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// RecurringInvoiceID
    /// </summary>
    public string? RecurringInvoiceId { get; set; }

    /// <summary>
    /// If mobile money, this is the transaction id of the send money transaction
    /// </summary>
    public string? RefundTransactionId { get; set; }

    /// <summary>
    /// The date the Business requested the refund
    /// </summary>
    public DateTime? RefundRequestedDate { get; set; }

    /// <summary>
    /// The date Hubtel completed the refund
    /// </summary>
    public DateTime? RefundCompletedDate { get; set; }

    /// <summary>
    /// Employee who requested for a Refund
    /// </summary>
    public string? RefundRequestedBy { get; set; }

    /// <summary>
    /// MobileMoney or Card
    /// </summary>
    public string? RefundDestinationType { get; set; }

    /// <summary>
    /// MobileMoney Number or Masked Card Number
    /// </summary>
    public string? RefundDestination { get; set; }

    /// <summary>
    /// Amount that will be refunded.
    /// </summary>
    public decimal AmountRefunded { get; set; }

    /// <summary>
    /// Pending || Completed || Rejected
    /// </summary>
    public string? RefundStatus { get; set; }

    public string? PaymentProcessorRoute { get; set; }
    public string? PaymentProcessorName { get; set; }
}