using Akka.Actor;
using Akka.Hosting;
using Hubtel.Kafka.Host.Core;
using Hubtel.Orders.Orderpaid.PG.Consumer.Actors;
using Hubtel.Orders.Orderpaid.PG.Consumer.Actors.Messages;
using Hubtel.Orders.Orderpaid.PG.Consumer.Data;
using Hubtel.Orders.Orderpaid.PG.Consumer.Extensions;
using Hubtel.Orders.Orderpaid.PG.Consumer.Models;
using Hubtel.Orders.Orderpaid.PG.Consumer.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hubtel.Orders.Orderpaid.PG.Consumer;

public class DbConsumer : KafkaConsumerBase
{
    private readonly ILogger _logger;
    private readonly ActorRegistry _actorRegistry;

    public DbConsumer(ILogger<DbConsumer> logger,
        ActorRegistry actorRegistry)
    {
        _logger = logger;
        _actorRegistry = actorRegistry;
    }
    
    [ConsumeTopic(FromType = typeof(IOptions<KafkaConsumerConfig>),
        PropertyName = nameof(KafkaConsumerConfig.TopicsAsSingleString))]
    public async Task HandleBulkMessage(List<Order> messages)
    {
        _logger.LogInformation("received bulk message with count: {Count}",messages.Count);
        var actor = await _actorRegistry.GetAsync<DbActor>();
        actor.Tell(new PersistToDbMessage(messages));
    }
    
    [ConsumeTopic(FromType = typeof(IOptions<KafkaExtra>),
        PropertyName = nameof(KafkaExtra.OrdersRetryTopic))]
    public async Task HandleOrdersRetryMessages(List<Order> messages)
    {
        _logger.LogInformation("received Orders retry message with count: {Count}",messages.Count);
        var actor = await _actorRegistry.GetAsync<DbActor>();
        actor.Tell(new PersistToDbMessage(messages));
    }
    [ConsumeTopic(FromType = typeof(IOptions<KafkaExtra>),
        PropertyName = nameof(KafkaExtra.PaymentRetryTopic))]
    public async Task HandlePaymentRetryMessages(List<Order> messages)
    {
        _logger.LogInformation("received Payment retry message with count: {Count}",messages.Count);
        var actor = await _actorRegistry.GetAsync<DbActor>();
        actor.Tell(new PersistPaymentMessage(messages));
    }
    
    [ConsumeTopic(FromType = typeof(IOptions<KafkaExtra>),
        PropertyName = nameof(KafkaExtra.OrdersItemsRetryTopic))]
    public async Task HandleOrderItemRetryMessages(List<Order> messages)
    {
        _logger.LogInformation("received Order Items retry message with count: {Count}",messages.Count);
        var actor = await _actorRegistry.GetAsync<DbActor>();
        actor.Tell(new PersistOrderItemMessage(messages));
    }
    
    [ConsumeTopic(FromType = typeof(IOptions<KafkaExtra>),
        PropertyName = nameof(KafkaExtra.InvoiceAdditionRetryTopic))]
    public async Task HandleInvoiceAdditionRetryMessages(List<Order> messages)
    {
        _logger.LogInformation("received retry message with count: {Count}",messages.Count);
        var actor = await _actorRegistry.GetAsync<DbActor>();
        actor.Tell(new PersistInvoiceAdditionMessage(messages));
    }
    

    public override Task ConsumingStopped()
    {
        _logger.LogWarning("consumer stopped at {DateTime}", DateTime.Now);
        return Task.CompletedTask;
    }
}