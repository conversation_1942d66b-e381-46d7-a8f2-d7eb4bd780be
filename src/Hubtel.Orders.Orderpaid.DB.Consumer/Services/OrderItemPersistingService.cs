using Hubtel.Orders.Orderpaid.PG.Consumer.Extensions;
using Hubtel.Orders.Orderpaid.PG.Consumer.Models;
using Hubtel.Orders.Orderpaid.PG.Consumer.Repositories;
using Microsoft.Extensions.Logging;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Services;

public class OrderItemPersistingService:IOrderItemPersistingService
{
    private readonly IUnifiedSalesDbRepository _unifiedSalesDbRepository;
    private readonly ILogger<OrderItemPersistingService> _logger;

    public OrderItemPersistingService(IUnifiedSalesDbRepository unifiedSalesDbRepository,
        ILogger<OrderItemPersistingService> logger)
    {
        _unifiedSalesDbRepository = unifiedSalesDbRepository;
        _logger = logger;
    }

    public async Task<bool> PersistOrderItem(List<OrderItemsEntity> orderItems)
    {
        if (orderItems.Count != 0)
        {
            _logger.LogInformation("Persisting order items to DB with count {Count}", orderItems.Count);
            var orderItemsResponse = await _unifiedSalesDbRepository.AddOrderItemsEntity(orderItems);
            if (!orderItemsResponse)
            {
                _logger.LogError("Failed to add order item to DB {OrderItem}", orderItems.Serialize());
                return false;
            }
            _logger.LogInformation("order items persisted successfully {payload}", orderItems.Serialize());
            return orderItemsResponse;
        }
        return false;
    }
}