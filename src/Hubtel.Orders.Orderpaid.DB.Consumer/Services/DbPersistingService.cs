using Hubtel.Kafka.Producer.Sdk.Options;
using Hubtel.Kafka.Producer.Sdk.Services;
using Hubtel.Orders.Orderpaid.PG.Consumer.Extensions;
using Hubtel.Orders.Orderpaid.PG.Consumer.Models;
using Hubtel.Orders.Orderpaid.PG.Consumer.Options;
using Hubtel.Orders.Orderpaid.PG.Consumer.Repositories;
using Mapster;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Services;

public class DbPersistingService: IDbPersistingService
{
    private readonly IUnifiedSalesDbRepository _unifiedSalesDbRepository;
    private readonly ILogger<DbPersistingService> _logger;
    private readonly CardVerificationConfig _config;
    private readonly IKafkaProducer _kafkaProducer;
    private readonly KafkaExtra _kafkaExtra;

    public DbPersistingService(IUnifiedSalesDbRepository unifiedSalesDbRepository, 
        ILogger<DbPersistingService> logger, 
        IOptions<CardVerificationConfig> config, 
        IKafkaProducerFactory kafkaProducerClientFactory, 
        IOptions<KafkaExtra> kafkaExtra, 
        IOptions<KafkaProducerConfig> kafkaProducerConfig)
    {
        _unifiedSalesDbRepository = unifiedSalesDbRepository;
        _logger = logger;
        var kafkaProducerConfiguration = kafkaProducerConfig.Value;
        _kafkaProducer = kafkaProducerClientFactory.CreateKafkaProducer(kafkaProducerConfiguration.Hosts[0].Alias);
        _kafkaExtra = kafkaExtra.Value;
        _config = config.Value;
    }

    public async Task<bool> DoDbPersistOperations(List<Order> orders)
    {
        var invoiceAddition = await GetInvoiceAdditions(orders);
        var payment = await GetPayment(orders);
        var orderEntity = await GetOrder(orders);
        var orderItem = await GetOrderItems(orders, payment);
        var orderResponse = await _unifiedSalesDbRepository.AddOrdersEntity(orderEntity);
        if (!orderResponse)
        {
            _logger.LogError("Failed to add {Count} orders to DB", orders.Count);
            foreach (var order in orders)
            {
                var ordersToRetry = order.Serialize();
                _logger.LogInformation("Retrying Order for order with id {OrderId}", order.Id);
                _kafkaProducer.Produce(_kafkaExtra.OrdersRetryTopic, 
                        ordersToRetry);
            }
            return false;
        }

        var paymentResponse = await _unifiedSalesDbRepository.AddPaymentEntity(payment);
        if (!paymentResponse)
        {
            _logger.LogError("Failed to add {Count} payment to DB", payment.Count);
            foreach (var order in orders)
            {
                var ordersToRetry = order.Serialize();
                _logger.LogInformation("Retrying payment for order with id {OrderId}", order.Id);
                _kafkaProducer.Produce(_kafkaExtra.PaymentRetryTopic, 
                    ordersToRetry);
            }
            return false;
        }

        if (orderItem.Count != 0)
        {
            var orderItemsResponse = await _unifiedSalesDbRepository.AddOrderItemsEntity(orderItem);
            if (!orderItemsResponse)
            {
                _logger.LogError("Failed to add {Count} order item to DB", orderItem.Count);
                foreach (var order in orders)
                {
                    var ordersToRetry = order.Serialize();
                    _logger.LogInformation("Retrying order items for order with id {OrderId}", order.Id);
                    _kafkaProducer.Produce(_kafkaExtra.OrdersItemsRetryTopic, 
                        ordersToRetry);
                }
                return false;
            }
        }
        
        if (invoiceAddition.Count != 0)
        {
            var invoiceAdditionResponse = await _unifiedSalesDbRepository.AddInvoiceAdditionEntity(invoiceAddition);
            if (!invoiceAdditionResponse)
            {
                _logger.LogError("Failed to add {Count} addition to DB", invoiceAddition.Count);
                foreach (var order in orders)
                {
                    var ordersToRetry = order.Serialize();
                    _logger.LogInformation("Retrying invoice addition for order with id {OrderId}", order.Id);
                    _kafkaProducer.Produce(_kafkaExtra.InvoiceAdditionRetryTopic, 
                        ordersToRetry);
                }
                return false;
            }
        }
        return true;
    }
    

    public async Task<bool> DoRetryPaymentPersistOperations(List<Order> orders)
    {
        var invoiceAddition = await GetInvoiceAdditions(orders);
        var payment = await GetPayment(orders);
        var orderItem = await GetOrderItems(orders, payment);
        var paymentResponse = await _unifiedSalesDbRepository.AddPaymentEntity(payment);
        if (!paymentResponse)
        {
            _logger.LogError("Failed to add {Count} payment to DB", payment.Count);
            foreach (var order in orders)
            {
                var ordersToRetry = order.Serialize();
                _logger.LogInformation("Retrying payment for order with id {OrderId}", order.Id);
                _kafkaProducer.Produce(_kafkaExtra.PaymentRetryTopic, 
                    ordersToRetry);
            }
            return false;
        }

        if (orderItem.Count != 0)
        {
            var orderItemsResponse = await _unifiedSalesDbRepository.AddOrderItemsEntity(orderItem);
            if (!orderItemsResponse)
            {
                _logger.LogError("Failed to add {Count} order item to DB", orderItem.Count);
                foreach (var order in orders)
                {
                    var ordersToRetry = order.Serialize();
                    _logger.LogInformation("Retrying order items for order with id {OrderId}", order.Id);
                    _kafkaProducer.Produce(_kafkaExtra.OrdersItemsRetryTopic, 
                        ordersToRetry);
                }
                return false;
            }
        }
        
        if (invoiceAddition.Count != 0)
        {
            var invoiceAdditionResponse = await _unifiedSalesDbRepository.AddInvoiceAdditionEntity(invoiceAddition);
            if (!invoiceAdditionResponse)
            {
                _logger.LogError("Failed to add {Count} addition to DB", invoiceAddition.Count);
                foreach (var order in orders)
                {
                    var ordersToRetry = order.Serialize();
                    _logger.LogInformation("Retrying invoice addition for order with id {OrderId}", order.Id);
                    _kafkaProducer.Produce(_kafkaExtra.InvoiceAdditionRetryTopic, 
                        ordersToRetry);
                }
                return false;
            }
        }
        return true;
    }

    public async Task<bool> DoRetryOrderItemPersistOperations(List<Order> orders)
    {
        var invoiceAddition = await GetInvoiceAdditions(orders);
        var payment = await GetPayment(orders);
        var orderItem = await GetOrderItems(orders, payment);
        if (orderItem.Count != 0)
        {
            var orderItemsResponse = await _unifiedSalesDbRepository.AddOrderItemsEntity(orderItem);
            if (!orderItemsResponse)
            {
                _logger.LogError("Failed to add {Count} order item to DB", orderItem.Count);
                foreach (var order in orders)
                {
                    var ordersToRetry = order.Serialize();
                    _logger.LogInformation("Retrying order items for order with id {OrderId}", order.Id);
                    _kafkaProducer.Produce(_kafkaExtra.OrdersItemsRetryTopic, 
                        ordersToRetry);
                }
                return false;
            }
        }
        
        if (invoiceAddition.Count != 0)
        {
            var invoiceAdditionResponse = await _unifiedSalesDbRepository.AddInvoiceAdditionEntity(invoiceAddition);
            if (!invoiceAdditionResponse)
            {
                _logger.LogError("Failed to add {Count} addition to DB", invoiceAddition.Count);
                foreach (var order in orders)
                {
                    var ordersToRetry = order.Serialize();
                    _kafkaProducer.Produce(_kafkaExtra.InvoiceAdditionRetryTopic, 
                        ordersToRetry);
                }
                return false;
            }
        }
        return true;
    }

    public async Task<bool> DoRetryInvoiceAdditionPersistOperations(List<Order> orders)
    {
        var invoiceAddition = await GetInvoiceAdditions(orders);
        if (invoiceAddition.Count != 0)
        {
            var invoiceAdditionResponse = await _unifiedSalesDbRepository.AddInvoiceAdditionEntity(invoiceAddition);
            if (!invoiceAdditionResponse)
            {
                _logger.LogError("Failed to add {Count} addition to DB", invoiceAddition.Count);
                foreach (var order in orders)
                {
                    var ordersToRetry = order.Serialize();
                    _logger.LogInformation("Retrying invoice addition for order with id {OrderId}", order.Id);
                    _kafkaProducer.Produce(_kafkaExtra.InvoiceAdditionRetryTopic, 
                        ordersToRetry);
                }
                return false;
            }
        }
        return true;
    }

    private async Task<List<OrderEntity>> GetOrder(List<Order> order)
    {
        await Task.CompletedTask;
        var orders = order.Select(a => a.Adapt<OrderEntity>()).ToList();
        var orderEntity = orders.Select(a => a.Adapt<OrderEntity>()).ToList();
        return orderEntity;
    }
    private async Task<List<PaymentEntity>> GetPayment(List<Order> orders)
    {
        await Task.CompletedTask;
        
        var payment = orders.Select(a => a.Payment).ToList();
        
        var paymentEntity = payment.Select(a=>a.Adapt<PaymentEntity>()).ToList();
        
        foreach (var entity in paymentEntity)
        {
            var order = orders.FirstOrDefault(o => o.Id == entity.OrderId);
            if (order != null)
            {
                entity.BusinessName = order.BusinessName;
                entity.CreatedAt = DateTime.UtcNow;
            }
            else
            {
                _logger.LogWarning("Order with ID {OrderId} not found for payment entity", entity.OrderId);
            }
        }
        
        paymentEntity = paymentEntity.Where(x => x.BusinessId != _config.BusinessId).ToList();
        
        return paymentEntity;
    }
    private async Task<List<OrderItemsEntity>> GetOrderItems(List<Order> orders, List<PaymentEntity> payment)
    {
        await Task.CompletedTask;
        var orderItems = orders.SelectMany(oi => oi.OrderItems).ToList();
        var orderItemsEntity = orderItems.Select(a => a.Adapt<OrderItemsEntity>()).ToList();
        for (var i = 0; i < orderItemsEntity.Count; i++)
        {
            var item = orderItemsEntity[i];
            var match = payment.FirstOrDefault(p => p.OrderId == item.OrderId);
            if (match != null)
            {
                item.BusinessId = match.BusinessId;
                orderItemsEntity[i] = item;
            }
        }
        return orderItemsEntity;
    }
    private async Task<List<InvoiceAdditionEntity>> GetInvoiceAdditions(List<Order> orders)
    {
        await Task.CompletedTask;
        var invoiceAdditions = orders.SelectMany(oi => oi.InvoiceAdditions).ToList();
        var invoiceAdditionsEntities
            = invoiceAdditions.Select(_ => invoiceAdditions.Adapt<InvoiceAdditionEntity>()).ToList();
        invoiceAdditionsEntities.ForEach(oie => oie.Id = Guid.NewGuid().ToString("N"));
        invoiceAdditionsEntities.ForEach(oie => oie.CreatedAt = DateTime.UtcNow);
        invoiceAdditionsEntities.ForEach(oie => oie.UpdatedAt = DateTime.UtcNow);
        return invoiceAdditionsEntities;
    }
    
}