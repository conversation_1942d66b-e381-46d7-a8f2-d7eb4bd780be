using Hubtel.Orders.Orderpaid.PG.Consumer.Models;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Services;

public interface IDbPersistingService
{
    Task<bool> DoDbPersistOperations(List<Order> orders);
    Task<bool> DoRetryPaymentPersistOperations(List<Order> orders);
    Task<bool> DoRetryOrderItemPersistOperations(List<Order> orders);
    Task<bool> DoRetryInvoiceAdditionPersistOperations(List<Order> orders);
}