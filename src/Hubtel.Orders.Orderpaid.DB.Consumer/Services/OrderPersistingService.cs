using Hubtel.Orders.Orderpaid.PG.Consumer.Extensions;
using Hubtel.Orders.Orderpaid.PG.Consumer.Models;
using Hubtel.Orders.Orderpaid.PG.Consumer.Repositories;
using Microsoft.Extensions.Logging;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Services;

public class OrderPersistingService : IOrderPersistingService
{
    private readonly IUnifiedSalesDbRepository _unifiedSalesDbRepository;
    private readonly ILogger<OrderPersistingService> _logger;

    public OrderPersistingService(IUnifiedSalesDbRepository unifiedSalesDbRepository, 
        ILogger<OrderPersistingService> logger)
    {
        _unifiedSalesDbRepository = unifiedSalesDbRepository;
        _logger = logger;
    }

    public async Task<bool> PersistOrderItem(OrderEntity orderEntity)
    {
        // var orderResponse = await _unifiedSalesDbRepository.AddOrdersEntity(orderEntity);
        // if (!orderResponse)
        // {
        //     _logger.LogError("Failed to add order to DB {Order}", orderEntity.Serialize());
        //     return false;
        // }
        // _logger.LogInformation("Order persisted successfully {payload}", orderEntity.Serialize());
        // return orderResponse;
        await Task.CompletedTask;
        return true;
    }
}