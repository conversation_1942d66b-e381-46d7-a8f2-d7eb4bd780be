using Hubtel.Orders.Orderpaid.PG.Consumer.Extensions;
using Hubtel.Orders.Orderpaid.PG.Consumer.Models;
using Hubtel.Orders.Orderpaid.PG.Consumer.Options;
using Hubtel.Orders.Orderpaid.PG.Consumer.Repositories;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Services;

public class PaymentPersistingService:IPaymentPersistingService
{
    private readonly CardVerificationConfig _config;
    private readonly IUnifiedSalesDbRepository _unifiedSalesDbRepository;
    private readonly ILogger<PaymentPersistingService> _logger;

    public PaymentPersistingService(IOptions<CardVerificationConfig> config, 
        ILogger<PaymentPersistingService> logger, 
        IUnifiedSalesDbRepository unifiedSalesDbRepository)
    {
        _logger = logger;
        _unifiedSalesDbRepository = unifiedSalesDbRepository;
        _config = config.Value;
    }

    public async Task<bool> PersistPayment(PaymentEntity payment)
    {
        // if (_config.BusinessId != payment.BusinessId)
        // {
        //     var paymentResponse = await _unifiedSalesDbRepository.AddPaymentEntity(payment);
        //     if (!paymentResponse)
        //     {
        //         _logger.LogError("Failed to add payment to DB {Payment}", payment.Serialize());
        //         return false;
        //     }
        //     _logger.LogInformation("Payment persisted successfully {payload}", payment.Serialize());
        //     return paymentResponse;
        // }
        await Task.CompletedTask;
        return false;
    }
}