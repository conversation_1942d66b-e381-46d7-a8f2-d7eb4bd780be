using Hubtel.Orders.Orderpaid.PG.Consumer.Extensions;
using Hubtel.Orders.Orderpaid.PG.Consumer.Models;
using Hubtel.Orders.Orderpaid.PG.Consumer.Repositories;
using Microsoft.Extensions.Logging;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Services;

public class InvoiceAdditionsPersistingService:IInvoiceAdditionsPersistingService
{
    private readonly IUnifiedSalesDbRepository _unifiedSalesDbRepository;
    private readonly ILogger<InvoiceAdditionsPersistingService> _logger;

    public InvoiceAdditionsPersistingService(IUnifiedSalesDbRepository unifiedSalesDbRepository, 
        ILogger<InvoiceAdditionsPersistingService> logger)
    {
        _unifiedSalesDbRepository = unifiedSalesDbRepository;
        _logger = logger;
    }

    public async Task<bool> PersistInvoiceAddition(List<InvoiceAdditionEntity> invoiceAdditions)
    {
        if (invoiceAdditions.Count != 0)
        {
            _logger.LogInformation("Persisting invoice additions to DB with count {Count}", invoiceAdditions.Count);
            var resp = await _unifiedSalesDbRepository.AddInvoiceAdditionEntity(invoiceAdditions);
            if (!resp)
            {
                _logger.LogError("Failed to add invoice additions to DB {InvoiceAdditions}",
                    invoiceAdditions.Serialize());
                return false;
            }
            return resp;
        }
        return false;
    }
}