using Akka.Actor;
using Hubtel.Instrumentation.Extensions;
using Hubtel.Kafka.Host;
using Hubtel.Kafka.Host.Core;
using Hubtel.Kafka.Host.Core.ConsumerLogic;
using Hubtel.Kafka.Producer.Sdk.Extensions;
using Hubtel.Orders.Orderpaid.PG.Consumer.Data;
using Hubtel.Orders.Orderpaid.PG.Consumer.Extensions;
using Hubtel.Orders.Orderpaid.PG.Consumer.Options;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Hubtel.Orders.Orderpaid.PG.Consumer.Models;
using Hubtel.Orders.Orderpaid.PG.Consumer.Repositories;
using Hubtel.Orders.Orderpaid.PG.Consumer.Services;
using KafkaProducerConfig = Hubtel.Kafka.Producer.Sdk.Options.KafkaProducerConfig;
using Confluent.Kafka;
using Confluent.Kafka.Admin;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

var host = CreateKafkaBuilder(args).Build();


ResolveActorSystem(host);
await CreateTopicsIfNotExistAsync(host);
await host.RunAsync();



static IHostBuilder CreateKafkaBuilder(string[] args) => KafkaHost
    .CreateDefaultBuilder(args)
    .ConfigureServices((hostContext, services) =>
    {
        //mandatory
        services.Configure<KafkaConsumerConfig>(options => hostContext.Configuration
            .GetSection(nameof(KafkaConsumerConfig)).Bind(options));
        services.Configure<KafkaExtra>(options => hostContext.Configuration
            .GetSection(nameof(KafkaExtra)).Bind(options));
        services.Configure<CardVerificationConfig>(options => hostContext.Configuration.GetSection(
            nameof(CardVerificationConfig)).Bind(options));
        services.Configure<ActorConfig>(options => hostContext.Configuration.GetSection(
            nameof(ActorConfig)).Bind(options));

        //uncomment these lines for bulk-consume
        services.Configure<MessageGroupConsumerLogicConfig>(options => hostContext.Configuration.GetSection(nameof(MessageGroupConsumerLogicConfig)).Bind(options));
        services.AddSingleton<IKafkaConsumerLogicBase, MessageGroupConsumerLogic<Order>>();
        

        services.AddDbContextFactory<ApplicationDbContext>(options => options
            .UseNpgsql(hostContext.Configuration.GetConnectionString("DbConnection")), 
            ServiceLifetime.Scoped);

        services.AddHubtelTelemetry(hostContext.Configuration);

        services.AddHubtelGelfLogging(hostContext.Configuration);

        services.AddHubtelStatsD(hostContext.Configuration);
        services.AddHubtelKafkaProducerSdk(c=>hostContext.Configuration
            .GetSection(nameof(KafkaProducerConfig)).Bind(c));
        
        services.AddScoped<IUnifiedSalesDbRepository, UnifiedSalesDbRepository>();
        services.AddScoped<IDbPersistingService, DbPersistingService>();
        services.AddScoped<IOrderPersistingService, OrderPersistingService>();
        services.AddScoped<IInvoiceAdditionsPersistingService, InvoiceAdditionsPersistingService>();
        services.AddScoped<IOrderItemPersistingService, OrderItemPersistingService>();
        services.AddScoped<IPaymentPersistingService, PaymentPersistingService>();

        //Register Service Here

        services.AddHubtelActorSystem(c=>hostContext.Configuration.GetSection(nameof(ActorConfig)).Bind(c));
    });

static void ResolveActorSystem(IHost host)
{
    var actorSystem = host.Services.GetRequiredService<ActorSystem>();
    _ = actorSystem ?? throw new ArgumentOutOfRangeException($"\"{actorSystem!.Name}\" not created or registered");
}

static async Task CreateTopicsIfNotExistAsync(IHost host)
{
    try
    {
        using var scope = host.Services.CreateScope();
        var kafkaConsumerConfig = scope.ServiceProvider.GetRequiredService<IOptions<KafkaConsumerConfig>>().Value;
        var kafkaExtra = scope.ServiceProvider.GetRequiredService<IOptions<KafkaExtra>>().Value;
        // Get Topics from KafkaExtra
        var topics = new List<string>
        {
            kafkaExtra.OrdersRetryTopic,
            kafkaExtra.PaymentRetryTopic,
            kafkaExtra.OrdersItemsRetryTopic,
            kafkaExtra.InvoiceAdditionRetryTopic
        };
        // Set up the AdminClient configuration with BootstrapServers
        var config = new AdminClientConfig { BootstrapServers = kafkaConsumerConfig.BootstrapServers };
        // Create the AdminClient using the configuration
        using var adminClient = new AdminClientBuilder(config).Build();
        // Get the metadata for all topics in the cluster, including the number of partitions and the replication factor for each topic
        var metadata = adminClient.GetMetadata(TimeSpan.FromSeconds(10));
        // Get the names of all existing topics
        var existingTopics = metadata.Topics.Select(t => t.Topic).ToHashSet();
        // Get the names of all topics that need to be created
        var topicsToCreate = topics.Where(topic => !existingTopics.Contains(topic))
            .Select(topic => new TopicSpecification
            {
                Name = topic,
                NumPartitions = kafkaConsumerConfig.AutoCreateTopics.NumberOfPartitions,
                ReplicationFactor = kafkaConsumerConfig.AutoCreateTopics.ReplicationFactor
            })
            .ToList();

        if (topicsToCreate.Count != 0)
        {
            // Create the topics
            await adminClient.CreateTopicsAsync(topicsToCreate);
        }
    }
    catch (Exception e)
    {
        var logger = host.Services.GetRequiredService<ILogger<Program>>();
        logger.LogWarning(e, "Error creating topics");
    }
    
}

