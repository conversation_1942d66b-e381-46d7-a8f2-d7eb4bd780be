using System.Reflection;
using System.Text.RegularExpressions;
using Akka.Actor;
using Akka.Hosting;
using Akka.Routing;
using Gelf.Extensions.Logging;
using Hubtel.Orders.Orderpaid.PG.Consumer.Actors;
using Hubtel.Orders.Orderpaid.PG.Consumer.Options;
using JustEat.StatsD;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Hubtel.Orders.Orderpaid.PG.Consumer.Extensions;

public static class ServiceCollectionExtension
{
    public static IServiceCollection AddHubtelActorSystem(
        this IServiceCollection services,Action<ActorConfig> configure)
    {
        services.Configure(configure);
        var actorConfig = new ActorConfig();
        configure.Invoke(actorConfig);
        var actorSystemName = Regex.Replace(Assembly.GetExecutingAssembly().GetName().Name ?? "ActorSystemName",
            @"[^a-zA-Z\s]+", "", RegexOptions.None, TimeSpan.FromMilliseconds(100));

        services.AddAkka(actorSystemName, (builder) =>
        {   
            builder.WithActors((system, registry, resolver) =>
            {
                var defaultStrategy = new OneForOneStrategy(
                    3, TimeSpan.FromSeconds(3), ex =>
                    {
                        if (ex is not ActorInitializationException)
                            return Directive.Resume;

                        system?.Terminate().Wait(1000);

                        return Directive.Stop;
                    });
                var dbActorProps = resolver
                    .Props<DbActor>()
                    .WithSupervisorStrategy(defaultStrategy)
                    .WithRouter(new RoundRobinPool(actorConfig.DbActorConfig.NumberOfInstances, 
                        new DefaultResizer(actorConfig.DbActorConfig.NumberOfInstances, 
                            actorConfig.DbActorConfig.UpperBound)));
                var dbActor = system.ActorOf(dbActorProps, nameof(DbActor));
                registry.Register<DbActor>(dbActor);
            });
        });

        return services;
    }

    public static void AddHubtelGelfLogging(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddLogging(loggingBuilder => loggingBuilder
            .AddConfiguration(configuration.GetSection("Logging"))
            .ClearProviders()
            .SetMinimumLevel(LogLevel.Debug)
            .AddConsole()
            .AddGelf(options =>
            {
                options.AdditionalFields = new Dictionary<string, object?>
                {
                    { "facility", configuration.GetSection("Logging")["GELF:Facility"] ?? string.Empty },
                    { "Environment", configuration.GetSection("Logging")["GELF:Environment"] ?? string.Empty },
                    { "machine_name", Environment.MachineName }
                };
                options.Host = configuration.GetSection("Logging")["GELF:Host"];
                options.LogSource = configuration.GetSection("Logging")["GELF:LogSource"];
                options.Port = int.Parse(configuration.GetSection("Logging")["GELF:Port"] ?? "");
            }));
    }

    public static void AddHubtelStatsD(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddStatsD(provider =>
        {
            var logger = provider.GetService<ILogger<Program>>();
            return new StatsDConfiguration
            {
                Host = configuration["StatsdConfig:Server"],
                Port = int.Parse(configuration["StatsdConfig:Port"] ?? string.Empty),
                Prefix = configuration["StatsdConfig:Prefix"] ?? string.Empty,
                OnError = (ex) =>
                {
                    logger?.LogError(ex, ex.Message);
                    return true;
                }
            };
        });
    }
}