using Akka.Actor;
using Akka.Event;
using Autofac;
using Microsoft.Extensions.Logging;

namespace Hubtel.Orders.Consumers.Common.Actors
{
    public class SimpleLoggerActor : ReceiveActor
    {
        private readonly ILoggerFactory _loggerFactory;


        public SimpleLoggerActor()
        {
            _loggerFactory = TopLevelActors.ServiceProvider.Resolve<ILoggerFactory>();
            
            Receive<Debug>(e =>
            {
                var logger = _loggerFactory.CreateLogger(e.LogClass);
                logger.LogDebug($"[{e.Thread.ManagedThreadId}]=> {e.Message} ");
            });
            Receive<Info>(e =>
            {
                var logger = _loggerFactory.CreateLogger(e.LogClass);
                logger.LogInformation($"[{e.Thread.ManagedThreadId}]=> {e.Message} ");
            });
            Receive<Warning>(e =>
            {
                var logger = _loggerFactory.CreateLogger(e.LogClass);
                logger.LogWarning($"[{e.Thread.ManagedThreadId}]=> {e.Message} ");
            });
            Receive<Error>(e =>
            {
                var logger = _loggerFactory.CreateLogger(e.LogClass);
                logger.LogWarning($"[{e.Thread.ManagedThreadId}]=> {e.Message} ");
            });
            Receive<DeadLetter>(e =>
            {
                var logger = _loggerFactory.CreateLogger(e.GetType());
                logger.LogError($"[ERROR]=> {e.Message} from {e.Sender.Path} to {e.Recipient.Path} ");
            });
            Receive<InitializeLogger>(_ =>
            {
                
                Sender.Tell(new LoggerInitialized());
            });
        }

       
    }
}
