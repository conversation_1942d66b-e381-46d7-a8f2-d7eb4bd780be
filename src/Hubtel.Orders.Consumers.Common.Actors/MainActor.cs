using System;
using System.Collections.Generic;
using System.Linq;
using Akka.DI.Core;
using Akka.Routing;
using Hubtel.Orders.Consumers.Common.Actors.Messages;
using Hubtel.Orders.Consumers.Common.Models;
using Microsoft.Extensions.Options;

namespace Hubtel.Orders.Consumers.Common.Actors
{

    public class MainActor : BaseActor
    {
        private readonly IPluginList _pluginList;
        private readonly List<OrderSchedulerPlugin> _plugins;


        /// <summary>
        /// </summary>
        public MainActor(
            IOptions<List<OrderSchedulerPlugin>> plugins, IPluginList pluginList
            )
        {
            _pluginList = pluginList;
            _plugins = plugins.Value;

            Receive<HandleBatchPayments>(m => DoHandleBatchOrders(m));
        }

        public override void AroundPreStart()
        {
            Logger.Debug($"initializing plugins");

            var handlers = _pluginList.HandlersList;

            foreach (var handler in handlers)
            {
                try
                {
                 
                   var pl = _plugins.FirstOrDefault(p => p.ActorName == handler.Name);

                   if (pl==null)
                   {
                       throw new Exception($"could not successfully find {handler.Name} in plugin list");
                   }
                   // var actorType = Type.GetType(plugin.Type);
                   var currentActor = Context.ActorOf(
                       Context.DI().Props(handler).WithRouter(new SmallestMailboxPool(pl.RouterInstances)),
                       handler.Name);

                    Logger.Debug($"created {pl.RouterInstances} instances of {pl.ActorName}");
                    Context.Dispatcher.EventStream.Subscribe(currentActor, typeof(HandleBatchPayments));
                    Logger.Debug($"subscribed {pl.ActorName} to HandleBatchPayments message");
                }
                catch (Exception e)
                {
                    Logger.Error(e, e.Message);
                    throw;
                }

            }
        }

        

        private void DoHandleBatchOrders(HandleBatchPayments message)
        {
            var validPayments
                = message.Payments.Where(o => !o.IsInvalidPayment()).ToList();

            Logger.Debug($"found {validPayments.Count}/{message.Payments.Count} valid payments.");
            if (!validPayments.Any())
            {
                Logger.Warning($"no valid payments found in list. Will not notify subscribers");
                return;
            }
            Logger.Debug($"about to publish {validPayments.Count} to all {_plugins.Count} subscribers");
            Publish(new HandleBatchPayments(validPayments));
        }
    }
}
