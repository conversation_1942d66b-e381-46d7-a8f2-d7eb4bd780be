using System;
using Akka.Actor;
using Akka.DI.AutoFac;
using Autofac;

namespace Hubtel.Orders.Consumers.Common.Actors
{
    public class TopLevelActors
    {

        public static IContainer ServiceProvider;
       
        //in case we run 2 or more instances
        public static string ActorSystemName = $"PaymentReceivedActorSystem{DateTime.UtcNow:ddMMyyyyHHmmss}";

        /// <summary>
        ///
        /// </summary>
        public static ActorSystem OrderSchedulerActorSystem;

        /// <summary>
        ///
        /// </summary>
        public static AutoFacDependencyResolver Resolver;
        
        /// <summary>
        /// 
        /// </summary>
        public static IActorRef MainActor = ActorRefs.Nobody;




        /// <summary>
        ///
        /// </summary>
        public static SupervisorStrategy GetDefaultSupervisorStrategy => new OneForOneStrategy(3,
            TimeSpan.FromSeconds(3),
            ex =>
            {
                if (ex is ActorInitializationException)
                {
                    Stop();
                    return Directive.Stop;
                }
                return Directive.Resume;
            });



        /// <summary>
        ///
        /// </summary>
        public static void Stop()
        {
            OrderSchedulerActorSystem.Terminate().Wait(1000);
        }
    }
}
