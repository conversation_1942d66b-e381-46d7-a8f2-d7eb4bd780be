{"Logging": {"LogLevel": {"Default": "Debug", "System": "Information", "Microsoft": "Information"}, "GELF": {"Host": "localhost", "Port": 12202, "LogSource": "Hubtel.Orders.Consumers.Paylink", "Facility": "Hubtel.Orders.Consumers.Paylink", "Environment": "Production", "LogLevel": {"Default": "Debug"}}}, "KafkaConsumerConfig": {"BootstrapServers": "localhost:9092", "GroupId": "Hubtel.Consumer.PaylinkWorkers", "Topics": ["hubtel.sales.order_payment_state_received"], "ExtraProperties": {"auto.offset.reset": "latest"}}, "PayLinkConfig": {"PayLinkUrl": "http://*************:9045"}, "KafkaProducerConfig": {"BootstrapServers": "localhost:9092"}, "ConsumerDataConfig": {"Url": "https://consumerdata.hubtel.com/api/Customer/SecDetails", "Authentication": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9tb2JpbGVwaG9uZSI6Ikh1YnRlbFVTU0QiLCJqdGkiOiI0ZDIwYmExYi0yZWY1LTRjYTMtYmEzYy1mYmFmNDU1MzY4NWIiLCJuYmYiOjE1NjMzNjYwNzUsImV4cCI6MTU5NDkwMjA3NCwiaXNzIjoiaHR0cDovL2h1YnRlbC5jb20iLCJhdWQiOiJodHRwOi8vaHVidGVsLmNvbSJ9.qLvyZYRUSMd2GF1w8dUjxY2Qo3fJco2xGxau_cz-jnQ"}, "KafkaExtra": {"PaylinkReceiveMoneyTopic": "hubtel.sales.order_payment_state_received", "PaylinkTransactionTopic": "hubtel.paylink.transaction.history", "MessagingTopic": "hubtel.messaging.update_paylink_amount", "ProducerMessagingTopic": "hubtel.producermessaging.update_paylink_amount", "PaylinkHistoryTopic": "hubtel.paylink.send_money_history", "PaylinkHistoryUpdateTopic": "hubtel.paylink.send_money_update_history", "RmSettlementInfoTopic": "hubtel.paylinks.rm.settlement.topic", "PaylinkHistoryRmCallbackTopic": "hubtel.paylink.receive_money_callback", "PaylinkHistoryDbTopic": "hubtel.messaging.database_paylink_history", "NotificationTopic": "hubtel.loyalty.upgrade.movement", "TriggerCallbackTopic": "trigger_paylink_consumer_callback", "UnVerifiedSettlementTopic": "hubtel.settle_unverified.paylink"}, "TemplateConfig": {"PayeeSettlementId": "payee_settlement_sms", "ConsumerSettlementId": "consumer_settlement_sms", "PayeeReportTemplateId": "open_paylink_notification_sms"}, "ProducerMessagingKafka": {"Topic": "hubtel.producermessaging.update_paylink_amount", "GroupId": "hubtel.producermessaging.paylink.workers", "BootstrapServers": "localhost:9092"}, "ApplicationInsights": {"InstrumentationKey": "xxx"}, "GhqrSettlementConfig": {"BaseUrl": "http://*************:9052"}, "StatsdConfig": {"Server": "localhost", "Port": 8125, "Prefix": "statsd_prefix_here"}, "MessageGroupConsumerLogicConfig": {"TimeoutInMilliseconds": 5000, "MaxElements": 200}, "ElasticSearchConfig": {"Index": "paylink_history", "Url": "https://search-testsearchstream-sy6zvow6tu7v5zhkp4g2bpxjby.eu-west-1.es.amazonaws.com"}}