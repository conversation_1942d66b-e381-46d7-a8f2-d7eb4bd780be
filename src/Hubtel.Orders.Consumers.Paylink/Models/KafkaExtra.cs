using System.Collections.Generic;

namespace Hubtel.Orders.Consumers.Paylink.Models
{
    public class KafkaExtra
    {
        public string PaylinkTransactionTopic { get; set; }
        public string MessagingTopic { get; set; }
        public string ProducerMessagingTopic { get; set; }
        
        public string PaylinkHistoryTopic { get; set; }
        public string PaylinkHistoryUpdateTopic { get; set; }
        public string PaylinkReceiveMoneyTopic { get; set; }
        public string RmSettlementInfoTopic { get; set; }
        
        public string PaylinkHistoryRmCallbackTopic { get; set; }
        
        public string UnVerifiedSettlementTopic { get; set; }
        public string NotificationTopic { get; set; }
        public string TriggerCallbackTopic { get; set; }

        public string PaylinkHistoryDbTopic { get; set; }

    }
}