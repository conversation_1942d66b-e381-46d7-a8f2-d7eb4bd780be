using System;
using Nest;

namespace Hubtel.Orders.Consumers.Paylink.Models
{
    public class PaylinkConsumerTransactionHistory
    {
        public string Id { get; set; }
        public string BusinessId { get; set; }
        public string ConsumerId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string  UpdatedBy { get; set; }
        public string IntegrationChannel { get; set; }
        public string PaylinkId { get; set; }
        public string PaylinkUrl { get; set; }
        public string SalesDeviceId { get; set; }
        public string SalesDeviceType { get; set; }
        public DateTime OrderDate { get; set; }
        public string OrderNumber { get; set; }
        public string Note { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string ResponseCode { get; set; }
        public string ProviderResponseCode { get; set; }
        public string ProviderName { get; set; }
        public string ProviderDescription { get; set; }
        public string EmployeeId { get; set; }
        public string EmployeeName { get; set; }
        public string PayeeMobileNumber { get; set; }
        public string PayeeName { get; set; }
        public string PayeeEmail { get; set; }
        public string FcmCustomer { get; set; }
        public string FcmDevice { get; set; }
        public float? DiscountRate { get; set; }
        public decimal? DiscountAmount { get; set; }
        public decimal Subtotal { get; set; }
        public decimal TotalAmountDue { get; set; }
        public string ReturnUrl { get; set; }
        public decimal? AmountPaid { get; set; }
        public decimal AmountRefunded { get; set; }
        public string PaymentType { get; set; }
        public string Location { get; set; }
        public bool IsSendMoney { get; set; }
        public string DeliveryLocation { get; set; }
        public string Currency { get; set; }
        public string LogoUrl { get; set; }
        public string Source { get; set; }
        public string Email { get; set; }
        public string Card { get; set; }
        public string CardTransactionId { get; set; }
        public decimal AmountDueCustomer { get; set; }
        public string PaylinkTitle { get; set; }
        public string PaylinkDescription { get; set; }
        
        public string PayLinkSourceType { get; set; }
        public string ScenarioType { get; set; }
        public string LinkedAccount { get; set; }
        public string LinkedAccountId { get; set; }
        public string LinkedAccountBankCode { get; set; }
        public string SenderId { get; set; }
        public string BankAccountNumber { get; set; }
        public string CancellationUrl { get; set; }
        public decimal AggregatedAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public decimal AmountRequested { get; set; }
        public string PaylinkUniqueId { get; set; }
        public string ClientReference { get; set; }

        public string PaymentReference { get; set; }
        public string MessageId { get; set; } 
        public bool IsPartialPay { get; set; }
        public string CallbackUrl { get; set; }
        public string TransactionId { get; set; }
        public bool IsConsumerVerified { get; set; }

        public string SettlementStatus { get; set; }
        public DateTime SettlementDate { get; set; }
        public string SettlementErrorMessage { get; set; }
        public Purpose Purpose { get; set; }
    }
}