using System;

namespace Hubtel.Orders.Consumers.Paylink.Models
{
    public class KafkaSendMoneyPaylinkHistory
    {
        public string Id { get; set; }
        public string ClientReference { get; set; }
        public decimal Amount { get; set; }
        public string CustomerName { get; set; }
        public string WithdrawalType { get; set; }

        public DateTime? CreatedAt { get; set; }
        public string PaylinkId { get; set; }
        public string Status { get; set; }
        public string ConsumerId { get; set; }
        public string BusinessId { get; set; }
        public string Description { get; set; }
        public string PayeeMobileNumber { get; set; }
        public string PayeeName { get; set; }
        public decimal AmountPaid { get; set; }
        public decimal AmountRequested { get; set; }
        public string PaymentReference { get; set; }
        public string BankAccountNumber { get; set; }

    }
}