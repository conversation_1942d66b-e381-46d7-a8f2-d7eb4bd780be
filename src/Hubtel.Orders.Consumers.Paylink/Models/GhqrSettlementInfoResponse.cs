using Newtonsoft.Json;

namespace Hubtel.Orders.Consumers.Paylink.Models
{
    public class GhqrSettlementInfoResponse
    {
        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("code")]
        public string Code { get; set; }

        [JsonProperty("data")]
        public GhqrSettlementInfo Data { get; set; }
    }

    public class GhqrSettlementInfo
    {
        [JsonProperty("savingsId")]
        public string SavingsId { get; set; }

        [JsonProperty("clientId")]
        public string ClientId { get; set; }

        [JsonProperty("settlementProvider")]
        public string SettlementProvider { get; set; }

        [JsonProperty("settlementAccountName")]
        public string SettlementAccountName { get; set; }

        [JsonProperty("settlementAccountNumber")]
        public string SettlementAccountNumber { get; set; }

        [JsonProperty("settlementBankCode")]
        public string SettlementBankCode { get; set; }

        [JsonProperty("settlementType")]
        public string SettlementType { get; set; }

        [JsonProperty("qrCode")]
        public string QrCode { get; set; }

        [JsonProperty("city")]
        public string City { get; set; }
    }
}