using Newtonsoft.Json;

namespace Hubtel.Orders.Consumers.Paylink.Models
{
    public class ConsumerVerificationResp
    {
        [JsonProperty("isOnTrustedList")]
        public bool IsOnTrustedList { get; set; }

        [JsonProperty("isOnBlacklist")]
        public bool IsOnBlacklist { get; set; }

        [JsonProperty("isVerified")]
        public bool IsVerified { get; set; }

        [JsonProperty("verificationStatus")]
        public string VerificationStatus { get; set; }

        [JsonProperty("accountVerificationNote")]
        public string AccountVerificationNote { get; set; }
    }
}