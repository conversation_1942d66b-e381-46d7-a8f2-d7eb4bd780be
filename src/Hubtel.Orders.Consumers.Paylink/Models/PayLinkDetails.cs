using System;
using Newtonsoft.Json;

namespace Hubtel.Orders.Consumers.Paylink.Models
{
    public class PayLinkDetails
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("phoneNumber")]
        public string PhoneNumber { get; set; }

        [JsonProperty("amount")]
        public decimal Amount { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("linkType")]
        public string LinkType { get; set; }

        [JsonProperty("link")]
        public string Link { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("customerName")]
        public string CustomerName { get; set; }

        [JsonProperty("clientReference")]
        public string ClientReference { get; set; }

        [JsonProperty("callbackUrl")]
        public string CallbackUrl { get; set; }

        [Json<PERSON>roperty("cancellationUrl")]
        public string CancellationUrl { get; set; }

        [JsonProperty("returnUrl")]
        public string ReturnUrl { get; set; }

        [JsonProperty("deepLink")]
        public string DeepLink { get; set; }

        [JsonProperty("audience")]
        public object[] Audience { get; set; }

        [JsonProperty("scenarioType")]
        public string ScenarioType { get; set; }

        [JsonProperty("isSecure")]
        public bool IsSecure { get; set; }

        [JsonProperty("hasIdentifier")]
        public bool HasIdentifier { get; set; }

        [JsonProperty("isPartialPay")]
        public bool IsPartialPay { get; set; }

        [JsonProperty("isSendMoney")]
        public bool IsSendMoney { get; set; }

        [JsonProperty("identifierAlias")]
        public string IdentifierAlias { get; set; }

        [JsonProperty("identifierDisplayName")]
        public string IdentifierDisplayName { get; set; }

        [JsonProperty("logo")]
        public string Logo { get; set; }

        [JsonProperty("currency")]
        public string Currency { get; set; }
        
        [JsonProperty("source")]
        public string Source { get; set; }
        
        [JsonProperty("businessId")]
        public string BusinessId { get; set; }

        [JsonProperty("referenceCode")]
        public string ReferenceCode { get; set; }

        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("updatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [JsonProperty("expireAt")]
        public DateTime? ExpireAt { get; set; }

        [JsonProperty("isBusiness")] 
        public bool IsBusiness { get; set; }
        
        [JsonProperty("transactionId")] 
        public string TransactionId { get; set; }
        
        [JsonProperty("senderId")] 
        public string SenderId { get; set; }
        
        [JsonProperty("purpose")] 
        public Purpose Purpose { get; set; } 
    }
    
    public class Purpose
    {    
        public string Id { get; set; }
        public string Name { get; set; }
        public string Url { get; set; }
    }
}