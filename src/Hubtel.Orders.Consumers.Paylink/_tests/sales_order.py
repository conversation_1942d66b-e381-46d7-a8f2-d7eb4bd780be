import json
from kafka import KafkaProducer

producer = KafkaProducer(bootstrap_servers=['127.0.0.1:9092'], api_version=(0, 10),
                         value_serializer=lambda m: json.dumps(m).encode('ascii'))
# produce asynchronously

payload = {
    "Id": "f264ecb771fa4ae7a747a5c81ae8f439",
    "IsEcommerceOrder": False,
    "CreatedAt": "2023-09-19T13:28:13.885717Z",
    "UpdatedAt": None,
    "CreatedBy": None,
    "UpdatedBy": None,
    "IntegrationChannel": "receive-money-proxy",
    "PosDeviceId": None,
    "BusinessId": "ae549a0bbd9e453fa3aa19a206b6cbc3",
    "PosDeviceType": None,
    "OrderDate": "2023-09-19T13:28:13.885717Z",
    "OrderNumber": "230919132813pch85869339",
    "Note": "SportyBet",
    "Description": "SportyBet",
    "Status": "Paid",
    "AssignedTo": None,
    "EmployeeId": "233241000046",
    "EmployeeName": "",
    "CustomerMobileNumber": "************",
    "CustomerName": "************",
    "BranchId": "dd3f5b9a233b41cb815e2817d535a437",
    "BranchName": "Main Branch",
    "TaxRate": None,
    "TaxAmount": None,
    "DiscountRate": None,
    "DiscountAmount": None,
    "Subtotal": 20.0,
    "TotalAmountDue": 20.0,
    "AmountPaid": 20.0,
    "AmountRefunded": 0.0,
    "PaymentTypes": None,
    "Location": None,
    "Currency": "GH",
    "OrderItems": [
        {
            "ItemId": "2fb9f84dfaf3451aa513fa4534d3c9c1",
            "OrderId": "f264ecb771fa4ae7a747a5c81ae8f439",
            "Name": " Bet Account TopUp",
            "Quantity": 1,
            "UnitPrice": 20.0,
            "ServiceRequestId": None,
            "ServiceData": None
        }
    ],
    "InvoiceAdditions": [],
    "IsFulfilled": None,
    "ConsumerRating": 0,
    "ConsumerFeedback": None,
    "CustomerEmail": None,
    "BusinessEmail": "<EMAIL>",
    "BusinessMobileNumber": "**********",
    "BusinessName": "SportyBet",
    "FcmCustomer": None,
    "FcmDevice": None,
    "AmountDueProducer": 0.0,
    "DeliveryFee": 0.0,
    "HasDelivery": False,
    "CustomerReward": 0.0,
    "SenderId": "SportyBet",
    "LogoUrl": "https://dev-hubtel.s3-eu-west-1.amazonaws.com/images/41ed85583dd74daf906ae1bf556fd170-**************.png",
    "ReturnUrl": None,
    "BranchEmail": None,
    "BranchPhoneNumber": None,
    "CancellationUrl": None,
    "DeliveryLocationName": None,
    "PaymentRequest": {
        "Channel": "mtn-gh",
        "CustomerMsisdn": "************",
        "PrimaryCallbackUrl": "https://www.sportybet.com/api/gh/pocket/c1/paych/hubtel/callback",
        "Token": None,
        "PaymentType": "MOMO",
        "FeesOnCustomer": False,
        "BusinessId": "ae549a0bbd9e453fa3aa19a206b6cbc3",
        "Amount": 20.0,
        "Description": "SportyBet",
        "ClientReference": "230919132813pch85869339",
        "OrderId": "f264ecb771fa4ae7a747a5c81ae8f439",
        "Currency": "GH",
        "Fee": 0.39,
        "DeliveryFee": 0.0,
        "SavingsAccountId": 11673,
        "AmountAfterCharges": 19.61,
        "BusinessName": "SportyBet",
        "ReceiptNumber": "********",
        "HubtelReference": "18AADA08948130655427",
        "AmountTendered": 0.0,
        "CustomerMobileNumber": "************",
        "TransactionId": None,
        "CustomerName": "************",
        "IsRefund": False,
        "Balance": 0.0,
        "OrderNumber": "230919132813pch85869339",
        "OrderDate": "0001-01-01T00:00:00",
        "IsRecurring": False,
        "RecurringInvoiceId": None,
        "RefundTransactionId": None,
        "RefundRequestedDate": None,
        "RefundCompletedDate": None,
        "RefundRequestedBy": None,
        "RefundDestinationType": None,
        "RefundDestination": None,
        "AmountRefunded": 0.0,
        "RefundStatus": None,
        "PaymentProcessorRoute": "hubtel.payments.receivemoney.mtn_gh.default_4",
        "PaymentProcessorName": "MTN GH Receive Money - Default-4"
    },
    "IsRecurring": False,
    "RecurringInvoiceId": None,
    "IsProgrammableService": False,
    "TotalProfit": 0.0,
    "Payment": {
        "Id": "b7fbdaa0e6944ea29bcdca42fda72a70",
        "BusinessId": "ae549a0bbd9e453fa3aa19a206b6cbc3",
        "CreatedAt": "2023-09-19T13:28:37.0586848Z",
        "UpdatedAt": None,
        "CreatedBy": None,
        "UpdatedBy": None,
        "PaymentType": "mobilemoney",
        "OrderId": "f264ecb771fa4ae7a747a5c81ae8f439",
        "MomoPhoneNumber": "************",
        "MomoChannel": "mtn-gh",
        "MomoToken": None,
        "TransactionId": "18AADA08948130655427",
        "ExternalTransactionId": "***********",
        "AmountAfterCharges": 19.61,
        "Charges": 0.39,
        "ChargeCustomer": False,
        "AmountPaid": 20.0,
        "PaymentDate": "2023-09-19T13:28:13.885717Z",
        "Note": None,
        "Description": "SportyBet",
        "PosDeviceId": None,
        "PosDeviceType": None,
        "EmployeeId": "233241000046",
        "EmployeeName": "",
        "CustomerMobileNumber": "************",
        "CustomerName": "************",
        "BranchId": "dd3f5b9a233b41cb815e2817d535a437",
        "BranchName": "Main Branch",
        "IsRefund": False,
        "IsSuccessful": True,
        "ReceiptNumber": "********",
        "Location": None,
        "Currency": "GH",
        "Scheme": None,
        "Card": None,
        "Tid": None,
        "Authorization": None,
        "Mid": None,
        "CardTransactionId": None,
        "AmountTendered": 0.0,
        "Balance": 0.0,
        "ClientReference": "230919132813pch85869339",
        "ProviderDescription": "The MTN Mobile Money payment has been approved and processed successfully.",
        "ProviderResponseCode": "SUCCESSFUL",
        "StatusCode": "0000",
        "FineractSavingsAccountId": 11673,
        "CardTransactionMode": None,
        "CardProcessor": None,
        "CallbackUrl": None,
        "IsRecurring": False,
        "RecurringInvoiceId": None,
        "RefundTransactionId": None,
        "RefundRequestedDate": None,
        "RefundCompletedDate": None,
        "RefundRequestedBy": None,
        "RefundDestinationType": None,
        "RefundDestination": None,
        "AmountRefunded": 0.0,
        "RefundStatus": None,
        "DeliveryFee": 0.0,
        "PaymentProcessor": "MTN GH Receive Money - Default-4"
    },
    "IsMultiCart": False,
    "IsMultiCartitem": False,
    "IsConsumerSettlement": False,
    "SalesDeviceId": None,
    "SalesDeviceType": None,
    "ConsumerSettlementNumber": None,
    "PaylinkUrl": None,
    "PaylinkId": None,
    "ScenarioType": None,
    "PaylinkTitle": None,
    "PaylinkDescription": None,
    "AggregatedAmount": 0.0,
    "RemainingAmount": 0.0,
    "AmountRequested": 0.0,
    "IsBusiness": True,
    "PayLinkSourceType": "SendMoney",
    "PaylinkUniqueId": None,
    "MessageId": None,
    "IsPartialPay": False,
    "NeedsGateKeeperCheck": False,
    "HasGateKeeper": False,
    "IsBlacklistedConsumer": False,
    "WalletId": 0,
    "PaylinkCallbackUrl": None,
    "BNPLDetails": None,
    "Country": None,
    "Region": None,
    "City": None,
    "Zone": None,
    "Station": None,
    "Longitude": None,
    "Latitude": None,
    "InvoiceId": None,
    "IsFirstPaymentInvoice": False,
    "IsELevyOrder": False,
    "DestinationData": None,
    "IsCardToBank": False,
    "CardVerification": None,
    "InstantServiceDetails": None,
    "ElevyDetails": None,
    "Kyc": None,
    "IsDirectDebitOrder": False,
    "PartnerBusinessId": None,
    "PartnerBranchId": None,
    "MultiCartIds": [],
    "Coupons": []
}

i = 0
while i < 5:
    producer.send('hubtel.sales.order_payment_state_received', value=
    payload)
    i = i + 1
    print("produced")
producer.flush()
