using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Akka.Actor;
using Hubtel.Kafka.Host.Core;
using Hubtel.Orders.Consumers.Common.Models;
using Hubtel.Orders.Consumers.Paylink.Components.Actors;
using Hubtel.Orders.Consumers.Paylink.Components.Actors.ActorMessages;
using Hubtel.Orders.Consumers.Paylink.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.Orders.Consumers.Paylink
{
    public class PaylinkOrderConsumer : KafkaConsumerBase
    {
        private readonly ILogger<PaylinkOrderConsumer> _logger;

        private readonly KafkaExtra _kafkaExtra;


        public PaylinkOrderConsumer(ILogger<PaylinkOrderConsumer> logger,
            IOptions<KafkaExtra> kafkaExtra
        )
        {
            _logger = logger;

            _kafkaExtra = kafkaExtra.Value;
        }

        //uncomment this for bulk read
        // [ConsumeTopic(FromType = typeof(IOptions<KafkaConsumerConfig>),
        //     PropertyName = nameof(KafkaConsumerConfig.TopicsAsSingleString))]
        // public async Task HandleBulkMessage(List<MyModel> messages)
        // {
        //     messages.ForEach(m => TopLevelActors.MainActor.Tell(m));
        //
        //     await Task.Delay(0);
        // }


        [ConsumeTopic(FromType = typeof(IOptions<KafkaConsumerConfig>),
            PropertyName = nameof(KafkaConsumerConfig.TopicsAsSingleString))]
        public async Task HandleReceiveMessage(Order message)
        {

            if (message.IsConsumerSettlement && message.Payment.IsSuccessful)
            {
                _logger.LogInformation($"about to start process: {message.ConsumerSettlementNumber}");

                TopLevelActors.MainActor.Tell(new ProcessPaylinkOrderMessage(message));
                await Task.Delay(0);
            }

            if (message.IsConsumerSettlement && !message.Payment.IsSuccessful)
            {
                TopLevelActors.MainActor.Tell(new ProcessFailedPaylinkOrderMessage(message));
                await Task.Delay(0);
            }
            
            if (message.IsBusiness && message.Payment.IsSuccessful)
            {
                _logger.LogInformation($"about to start business paylinks process");

                TopLevelActors.MainActor.Tell(new ProcessBusinessPaylinkOrderMessage(message));
                await Task.Delay(0);
            }
        }

        [ConsumeTopic(FromType = typeof(IOptions<KafkaExtra>),
            PropertyName = nameof(KafkaExtra.PaylinkHistoryTopic))]
        public async Task HandleMessage(KafkaSendMoneyPaylinkHistory message)
        {
            _logger.LogInformation($"about to start process: {message.PayeeMobileNumber}");

            TopLevelActors.MainActor.Tell(new ProcessPaylinkSendMoneyMessage(message));
            await Task.Delay(0);
        }


        [ConsumeTopic(FromType = typeof(IOptions<KafkaExtra>),
            PropertyName = nameof(KafkaExtra.PaylinkHistoryUpdateTopic))]
        public async Task HandleMessageUpdate(KafkaSendMoneyPaylinkHistory message)
        {
            _logger.LogInformation($"about to start process: {message.PayeeMobileNumber}");

            TopLevelActors.MainActor.Tell(new ProcessPaylinkSendMoneyMessageUpdate(message));
            await Task.Delay(0);
        }

        [ConsumeTopic(FromType = typeof(IOptions<KafkaExtra>),
            PropertyName = nameof(KafkaExtra.RmSettlementInfoTopic))]
        public async Task HandleRmSettlementUpdate(RmSettlementInfoDto message)
        {
            _logger.LogInformation($"about to start process: {message.Id}");

            TopLevelActors.MainActor.Tell(new ProcessRmSettlementInfoMessageUpdate(message));
            await Task.Delay(0);
        }
        
        [ConsumeTopic(FromType = typeof(IOptions<KafkaExtra>),
            PropertyName = nameof(KafkaExtra.PaylinkHistoryRmCallbackTopic))]
        public async Task HandleRmCallbackSettlementUpdate(RmSettlementCallbackInfo message)
        {
            _logger.LogInformation($"about to start process: {message.ClientReference}");

            TopLevelActors.MainActor.Tell(new ProcessRmCallbackSettlementInfoMessageUpdate(message));
            await Task.Delay(0);
        }
        
        [ConsumeTopic(FromType = typeof(IOptions<KafkaExtra>),
            PropertyName = nameof(KafkaExtra.UnVerifiedSettlementTopic))]
        public async Task HandleUnverifiedSettlementUpdate(UnverifiedSettlementStatus message)
        {
            _logger.LogInformation($"about to start process: {JsonConvert.SerializeObject(message)}");

            TopLevelActors.MainActor.Tell(new ProcessRmUnverifiedSettlementInfoMessageUpdate(message));
            await Task.Delay(0);
        }



        public override Task ConsumingStopped()
        {
            _logger.LogWarning($"consumer stopped at {DateTime.UtcNow}");
            return Task.CompletedTask;
        }
    }
}