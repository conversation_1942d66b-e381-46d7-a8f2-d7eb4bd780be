using Hubtel.Orders.Consumers.Common.Models;
using Hubtel.Orders.Consumers.Paylink.Models;

namespace Hubtel.Orders.Consumers.Paylink.Components.Actors.ActorMessages
{
    public struct ProcessPaylinkOrderMessage
    {
        public Order Model { get; }

        public ProcessPaylinkOrderMessage(Order model)
        {
            Model = model;
        }
    }
    
    public struct ProcessFailedPaylinkOrderMessage
    {
        public Order Model { get; }

        public ProcessFailedPaylinkOrderMessage(Order model)
        {
            Model = model;
        }
    }
    
    public struct ProcessBusinessPaylinkOrderMessage
    {
        public Order Model { get; }

        public ProcessBusinessPaylinkOrderMessage(Order model)
        {
            Model = model;
        }
    }
    
    
    public struct ProcessPaylinkSendMoneyMessage
    {
        public KafkaSendMoneyPaylinkHistory Model { get; }

        public ProcessPaylinkSendMoneyMessage(KafkaSendMoneyPaylinkHistory model)
        {
            Model = model;
        }
    }
    
    public struct ProcessPaylinkSendMoneyMessageUpdate
    {
        public KafkaSendMoneyPaylinkHistory Model { get; }

        public ProcessPaylinkSendMoneyMessageUpdate(KafkaSendMoneyPaylinkHistory model)
        {
            Model = model;
        }
    }
    
    
    public struct ProcessRmSettlementInfoMessageUpdate
    {
        public RmSettlementInfoDto Model { get; }

        public ProcessRmSettlementInfoMessageUpdate(RmSettlementInfoDto model)
        {
            Model = model;
        }
    }
    
    public struct ProcessRmCallbackSettlementInfoMessageUpdate
    {
        public RmSettlementCallbackInfo Model { get; }

        public ProcessRmCallbackSettlementInfoMessageUpdate(RmSettlementCallbackInfo model)
        {
            Model = model;
        }
    }
    
    
    public struct ProcessRmUnverifiedSettlementInfoMessageUpdate
    {
        public UnverifiedSettlementStatus Model { get; }

        public ProcessRmUnverifiedSettlementInfoMessageUpdate(UnverifiedSettlementStatus model)
        {
            Model = model;
        }
    }
}