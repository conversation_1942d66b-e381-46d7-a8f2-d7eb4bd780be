using System;
using Akka.Actor;

namespace Hubtel.Orders.Consumers.Paylink.Components.Actors
{
    public class TopLevelActors
    {
        public static IActorRef MainActor = ActorRefs.Nobody;
        public static ActorSystem ActorSystem;

        public static SupervisorStrategy GetDefaultSupervisorStrategy => new OneForOneStrategy(3,
            TimeSpan.FromSeconds(3),
            ex =>
            {
                if (!(ex is ActorInitializationException)) return Directive.Resume;
                Stop();
                return Directive.Stop;
            });

        /// <summary>
        /// This method stops the actor system
        /// </summary>
        private static void Stop()
        {
            ActorSystem?.Terminate().Wait(1000);
        }
    }
}