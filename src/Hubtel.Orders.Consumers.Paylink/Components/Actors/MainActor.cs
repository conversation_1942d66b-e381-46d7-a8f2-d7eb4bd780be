using System;
using System.Threading.Tasks;
using Confluent.Kafka;
using Hubtel.Kafka.Host.Core;
using Hubtel.Orders.Consumers.Paylink.Components.Actors.ActorMessages;
using Hubtel.Orders.Consumers.Paylink.Models;
using Hubtel.Orders.Consumers.Paylink.Options;
using Hubtel.Orders.Consumers.Paylink.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.Orders.Consumers.Paylink.Components.Actors
{
    public class MainActor : BaseActor
    {
        private readonly IEsRepository _esRepository;
        private readonly ILogger<MainActor> _logger;
        private readonly ISettlementInfoService _settlementInfoService;
        private readonly IKafkaRawProducer _kafkaRawProducer;
        private readonly IOptions<KafkaExtra> _kafkaExtra;
        private readonly IKafkaProducer _producerMessagingKafka;
        private readonly IOptions<ProducerMessagingKafka> _producerMessagingConfig;
        private readonly IOptions<TemplateConfig> _templateConfig;
        private readonly IPayLinkService _payLinkService;
        private readonly IConsumerVerificationService _consumerVerificationService;

        public MainActor(IEsRepository esRepository
            , ILogger<MainActor> logger, ISettlementInfoService settlementInfoService,
            IKafkaRawProducer kafkaRawProducer, IOptions<KafkaExtra> kafkaExtra, IKafkaProducer producerMessagingKafka, IOptions<ProducerMessagingKafka> producerMessagingConfig, IOptions<TemplateConfig> templateConfig, IPayLinkService payLinkService, IConsumerVerificationService consumerVerificationService)
        {
            _esRepository = esRepository;
            _logger = logger;
            _settlementInfoService = settlementInfoService;
            _kafkaRawProducer = kafkaRawProducer;
            _kafkaExtra = kafkaExtra;
            _producerMessagingKafka = producerMessagingKafka;
            _producerMessagingConfig = producerMessagingConfig;
            _templateConfig = templateConfig;
            _payLinkService = payLinkService;
            _consumerVerificationService = consumerVerificationService;
            ReceiveAsync<ProcessPaylinkOrderMessage>(DoProcessMyModel);
            ReceiveAsync<ProcessFailedPaylinkOrderMessage>(DoProcessFailedTransactions);
            ReceiveAsync<ProcessBusinessPaylinkOrderMessage>(DoProcessBusinessPaylinks);
            ReceiveAsync<ProcessPaylinkSendMoneyMessage>(DoSendMoneyToEs);
            ReceiveAsync<ProcessPaylinkSendMoneyMessageUpdate>(DoStatusUpdate);
            ReceiveAsync<ProcessRmSettlementInfoMessageUpdate>(DoRmSettlementUpdate);
            ReceiveAsync<ProcessRmCallbackSettlementInfoMessageUpdate>(DoRmCallbackSettlementUpdate);
            ReceiveAsync<ProcessRmUnverifiedSettlementInfoMessageUpdate>(DoRmUnverifiedSettlementUpdate);
        }

        private async Task DoRmUnverifiedSettlementUpdate(ProcessRmUnverifiedSettlementInfoMessageUpdate message)
        {
            var payload = await _esRepository.GetTransactionByField(message.Model.Id, "id");

            if (payload == null)
            {
                _logger.LogError($"Could not find payload for client_reference: {JsonConvert.SerializeObject(message)}");
                
                return;
            }

            var rmSettlementStatusUpdateDto = new UnverifiedSettlementStatus()
            {
                Id = payload.Id,
                Status = "verified",
            };
            
            await _esRepository.UpdateSettlementInfoVerifiedStatus(rmSettlementStatusUpdateDto);
        }

        private async Task DoRmCallbackSettlementUpdate(ProcessRmCallbackSettlementInfoMessageUpdate message)
        {
            var payload = await _esRepository.GetTransactionByField(message.Model.ClientReference, "paymentReference");

            if (payload == null)
            {
                _logger.LogError($"Could not find payload for client_reference: {message.Model.ClientReference}");
                
                return;
            }

            var rmSettlementStatusUpdateDto = new RmSettlementInfoDto
            {
                Id = payload.Id,
                SettlementDate = message.Model.SettlementDate,
                SettlementStatus = message.Model.SettlementStatus, 
                CallbackDescription = message.Model.CallbackDescription
            };
            
            await _esRepository.UpdateSettlementInfoStatus(rmSettlementStatusUpdateDto);

        }

        private async Task DoProcessBusinessPaylinks(ProcessBusinessPaylinkOrderMessage message)
        {
              var paylinkTransaction = new PaylinkConsumerTransactionHistory
                {
                    BusinessId = message.Model.BusinessId,
                    ConsumerId = message.Model.ConsumerSettlementNumber,
                    Id = message.Model.Id,
                    CreatedAt = message.Model.CreatedAt,
                    UpdatedBy = message.Model.UpdatedBy,
                    IntegrationChannel = message.Model.IntegrationChannel,
                    PaylinkId = message.Model.PaylinkId,
                    PaylinkUrl = message.Model.PaylinkUrl,
                    OrderDate = message.Model.OrderDate,
                    OrderNumber = message.Model.OrderNumber,
                    Note = message.Model.Note,
                    Description = message.Model.Description,
                    Status = message.Model.Status,
                    ResponseCode = message.Model.Payment?.ProviderResponseCode,
                    ProviderResponseCode = message.Model.Payment?.ProviderResponseCode,
                    ProviderName = message.Model.Payment?.PaymentType,
                    ProviderDescription = message.Model.Payment?.ProviderDescription,
                    EmployeeId = message.Model.Payment?.EmployeeId,
                    EmployeeName = message.Model.Payment?.EmployeeName,
                    PayeeMobileNumber = message.Model.CustomerMobileNumber,
                    PayeeEmail = message.Model.CustomerEmail,
                    PayeeName = message.Model.CustomerName,
                    Card = message.Model.Payment?.Card,
                    CardTransactionId = message.Model.Payment?.CardTransactionId,
                    FcmCustomer = message.Model.FcmCustomer,
                    FcmDevice = message.Model.FcmDevice,
                    DiscountAmount = message.Model.DiscountAmount,
                    DiscountRate = message.Model.DiscountRate,
                    SalesDeviceId = message.Model.SalesDeviceId,
                    SalesDeviceType = message.Model.SalesDeviceType,
                    Subtotal = message.Model.Subtotal,
                    TotalAmountDue = message.Model.TotalAmountDue,
                    ReturnUrl = message.Model.ReturnUrl,
                    AmountPaid = message.Model.Payment?.AmountAfterCharges,
                    AmountRefunded = message.Model.AmountRefunded,
                    PaymentType = message.Model.Payment?.PaymentType,
                    Location = message.Model.Location,
                    DeliveryLocation = message.Model.DeliveryLocationName,
                    ScenarioType = message.Model.ScenarioType,
                    Currency = message.Model.Currency,
                    LogoUrl = message.Model.LogoUrl,
                    AmountDueCustomer = message.Model.AmountPaid,
                    SenderId = message.Model.SenderId,
                    CancellationUrl = message.Model.CancellationUrl,
                    PaylinkTitle = message.Model.PaylinkTitle,
                    PaylinkDescription = message.Model.PaylinkDescription,
                    AmountRequested = message.Model.AmountRequested,
                    AggregatedAmount = message.Model.AggregatedAmount,
                    RemainingAmount = message.Model.RemainingAmount,
                    PaylinkUniqueId = message.Model.PaylinkUniqueId,
                    MessageId = message.Model.MessageId,
                    IsPartialPay = message.Model.IsPartialPay,
                    PayLinkSourceType = message.Model.PayLinkSourceType
                };

                var res = await _esRepository.Add(paylinkTransaction);
                
                if ("Multiple_Private_Open".Equals(message.Model.ScenarioType, StringComparison.OrdinalIgnoreCase))
                {
                    var payeeReportNotify = new NotificationDto
                    {
                        TemplateId = _templateConfig.Value.PayeeReportTemplateId,
                        Destination = paylinkTransaction.PayeeMobileNumber,
                        SenderId = paylinkTransaction.SenderId,
                        Type = "sms",
                        Data = new NotifierBody
                        {
                            Amount = message.Model.Payment?.AmountAfterCharges.ToString("N2"),
                            Currency = "GHS",
                            Url = $"https://p.hbtl.co/r/{paylinkTransaction.PaylinkId}"
                        }
                    };
                        
                    _logger.LogInformation($"payee report notification: {payeeReportNotify}");
                        
                    var payeeReport = await _kafkaRawProducer.Produce(_kafkaExtra.Value.NotificationTopic,
                        JsonConvert.SerializeObject(payeeReportNotify));
                    _logger.LogInformation(
                        $"pushed payee notification payload: {JsonConvert.SerializeObject(payeeReportNotify)} to number {paylinkTransaction.PayeeMobileNumber} returned status {payeeReport.Status}");
                }
                
                var dbDelResult = await _kafkaRawProducer.Produce(_kafkaExtra.Value.PaylinkHistoryDbTopic,
                    JsonConvert.SerializeObject(paylinkTransaction));

                _logger.LogInformation(
                    $"producing trigger callback payload for number: {paylinkTransaction.ConsumerId} ==> " +
                    $"\n payload: {JsonConvert.SerializeObject(paylinkTransaction)} \n " +
                    $"callbackUrl: {message.Model.PaylinkCallbackUrl}\n" +
                    $"payee number: {paylinkTransaction.PayeeMobileNumber} \n" +
                    $"kafka status: {dbDelResult.Status}");
                


                if (!string.IsNullOrEmpty(message.Model.PaylinkUniqueId))
                {
                    var partialPaymentDto = new PartialPaymentRequestDto
                    {
                        AggregatedAmount = message.Model.AggregatedAmount,
                        MessageId = message.Model.MessageId,
                        RemainingAmount = message.Model.RemainingAmount
                    };

                    await _producerMessagingKafka.Produce(partialPaymentDto, _producerMessagingConfig.Value.Topic);
                    
                    _logger.LogInformation(
                        $"paylink-partial pay for orderId: {paylinkTransaction.Id} ==> " +
                        $"\n payload: {JsonConvert.SerializeObject(partialPaymentDto)} \n " +
                        $"payee number: {paylinkTransaction.PayeeMobileNumber} \n");
                }
        }

        private async Task DoRmSettlementUpdate(ProcessRmSettlementInfoMessageUpdate message)
        {
            await _esRepository.UpdateSettlementInfoStatus(message.Model);
        }

        private async Task DoStatusUpdate(ProcessPaylinkSendMoneyMessageUpdate message)
        {
            await _esRepository.UpdateStatus(message.Model);
        }

        private async Task DoSendMoneyToEs(ProcessPaylinkSendMoneyMessage message)
        {
            try
            {
                
                var paylinkDetails = await _payLinkService.GetPaylinkDetails(message.Model.PaylinkId);

                var paylinkTransaction = new PaylinkConsumerTransactionHistory
                {
                    ConsumerId = message.Model.ConsumerId,
                    Id = message.Model.Id,
                    CreatedAt = message.Model.CreatedAt?? DateTime.Now,
                    PaylinkId = message.Model.PaylinkId,
                    OrderDate = message.Model.CreatedAt?? DateTime.Now,
                    Description = message.Model.Description,
                    Status = message.Model.Status,
                    BusinessId = message.Model.BusinessId,
                    IsSendMoney = true,
                    PayeeMobileNumber = message.Model.PayeeMobileNumber,
                    PayeeName = message.Model.PayeeName,
                    AmountPaid = message.Model.AmountPaid,
                    PaymentType = message.Model.WithdrawalType,
                    Currency = "GHS",
                    PaylinkDescription = message.Model.Description,
                    PaymentReference = message.Model.ClientReference,
                    AmountRequested = message.Model.AmountRequested,
                    BankAccountNumber = message.Model.BankAccountNumber,
                    CallbackUrl = paylinkDetails?.CallbackUrl,
                    ClientReference = paylinkDetails?.ClientReference,
                    Source = paylinkDetails?.Source,
                    TransactionId = paylinkDetails?.TransactionId

                };

                var res = await _esRepository.Add(paylinkTransaction);


                _logger.LogInformation($"for consumer number: {paylinkTransaction.ConsumerId}, \n" +
                                       $"for payee number: {paylinkTransaction.PayeeMobileNumber} \n" +
                                       $"persist response: {res.IsValid} ==> \n" +
                                       $"Debug Information: {res.DebugInformation}");
                
                
                if ("api".Equals(paylinkTransaction.Source,  StringComparison.OrdinalIgnoreCase))
                {
                    paylinkTransaction.Status = "success";
                    
                    var triggerResult = await _kafkaRawProducer.Produce(_kafkaExtra.Value.TriggerCallbackTopic,
                        JsonConvert.SerializeObject(paylinkTransaction));

                    _logger.LogInformation(
                        $"producing trigger callback payload for number: {paylinkTransaction.ConsumerId} ==> " +
                        $"\n payload: {JsonConvert.SerializeObject(paylinkTransaction)} \n " +
                        $"callbackUrl: {paylinkDetails?.CallbackUrl}\n" +
                        $"payee number: {paylinkTransaction.PayeeMobileNumber} \n" +
                        $"kafka status: {triggerResult.Status}");
                }
                
                
            }
            catch (Exception e)
            {
               _logger.LogError($"An error occured processing a payload \n" +
                                $"consumer number: {message.Model.ConsumerId} \n" +
                                $"payee number {message.Model.PayeeMobileNumber } \n" +
                                $"error: {e}");
            }
        }
        
        private async Task DoProcessFailedTransactions(ProcessFailedPaylinkOrderMessage message)
        {
            try
            {
                await _payLinkService.PaylinkPaymentAttempts(message.Model.PaylinkId, false);
                
                var settlementResp =
                    await _settlementInfoService.GetSettlementAccountInfo(message.Model.ConsumerSettlementNumber);
                
                var paylinkDetails = await _payLinkService.GetPaylinkDetails(message.Model.PaylinkId);
                
                var consumerResp = "HubtelSales".Equals(message.Model.IntegrationChannel, StringComparison.OrdinalIgnoreCase)?
                    await _consumerVerificationService.GetConsumerVerificationDetails(message.Model.ConsumerSettlementNumber):
                await _consumerVerificationService.GetConsumerVerificationDetails(paylinkDetails?.PhoneNumber);


                var paylinkTransaction = new PaylinkConsumerTransactionHistory
                {
                    ConsumerId = message.Model.ConsumerSettlementNumber,
                    Id = message.Model.Id,
                    CreatedAt = message.Model.CreatedAt,
                    UpdatedBy = message.Model.UpdatedBy,
                    IntegrationChannel = message.Model.IntegrationChannel,
                    PaylinkId = message.Model.PaylinkId,
                    PaylinkUrl = message.Model.PaylinkUrl,
                    OrderDate = message.Model.OrderDate,
                    OrderNumber = message.Model.OrderNumber,
                    Note = message.Model.Note,
                    Description = message.Model.Description,
                    Status = message.Model.Status,
                    ScenarioType = message.Model.ScenarioType,
                    ResponseCode = message.Model.Payment?.ProviderResponseCode,
                    ProviderResponseCode = message.Model.Payment?.ProviderResponseCode,
                    ProviderName = message.Model.Payment?.PaymentType,
                    ProviderDescription = message.Model.Payment?.ProviderDescription,
                    EmployeeId = message.Model.Payment?.EmployeeId,
                    EmployeeName = message.Model.Payment?.EmployeeName,
                    PayeeMobileNumber = message.Model.CustomerMobileNumber,
                    PayeeEmail = message.Model.CustomerEmail,
                    PayeeName = message.Model.CustomerName,
                    Card = message.Model.Payment?.Card,
                    CardTransactionId = message.Model.Payment?.CardTransactionId,
                    FcmCustomer = message.Model.FcmCustomer,
                    FcmDevice = message.Model.FcmDevice,
                    DiscountAmount = message.Model.DiscountAmount,
                    DiscountRate = message.Model.DiscountRate,
                    SalesDeviceId = message.Model.SalesDeviceId,
                    SalesDeviceType = message.Model.SalesDeviceType,
                    Subtotal = message.Model.Subtotal,
                    TotalAmountDue = message.Model.TotalAmountDue,
                    ReturnUrl = message.Model.ReturnUrl,
                    AmountPaid = message.Model.Payment?.AmountAfterCharges,
                    AmountRefunded = message.Model.AmountRefunded,
                    PaymentType = message.Model.Payment?.PaymentType,
                    Location = message.Model.Location,
                    DeliveryLocation = message.Model.DeliveryLocationName,
                    Currency = message.Model.Currency,
                    LogoUrl = message.Model.LogoUrl,
                    AmountDueCustomer = message.Model.AmountPaid,
                    SenderId = "api".Equals(paylinkDetails?.Source)? paylinkDetails?.SenderId: message.Model.SenderId,
                    CancellationUrl = message.Model.CancellationUrl,
                    PaylinkTitle = message.Model.PaylinkTitle,
                    PaylinkDescription = message.Model.PaylinkDescription,
                    LinkedAccount = settlementResp?.Data?.SettlementType,
                    LinkedAccountId = settlementResp?.Data?.SettlementAccountNumber,
                    LinkedAccountBankCode = settlementResp?.Data?.SettlementBankCode,
                    PaymentReference = $"paylink_{Guid.NewGuid().ToString("N")}",
                    AmountRequested = message.Model.AmountRequested,
                    AggregatedAmount = message.Model.AggregatedAmount,
                    RemainingAmount = message.Model.RemainingAmount,
                    PaylinkUniqueId = message.Model.PaylinkUniqueId,
                    MessageId = message.Model.MessageId,
                    IsPartialPay = message.Model.IsPartialPay,
                    CallbackUrl =  paylinkDetails?.CallbackUrl,
                    ClientReference = paylinkDetails?.ClientReference,
                    Source = paylinkDetails?.Source,
                    IsConsumerVerified = consumerResp?.IsVerified ?? false,
                    SettlementStatus = consumerResp is { IsOnTrustedList: false } ? "untrusted": null,
                    Purpose = paylinkDetails?.Purpose
                };

                var res = await _esRepository.AddFailedIndex(paylinkTransaction);

                _logger.LogDebug($"for consumer number: {paylinkTransaction.ConsumerId}, \n" +
                                 $"for payee number: {paylinkTransaction.PayeeMobileNumber} \n" +
                                 $"persist response: {res.IsValid} ==> \n" +
                                 $"Debug Information: {res.DebugInformation}");

            }
            catch (Exception e)
            {
               _logger.LogError($"An error occured processing a payload \n" +
                                $"consumer number: {message.Model.ConsumerSettlementNumber} \n" +
                                $"payee number {message.Model.CustomerMobileNumber} \n" +
                                $"error: {e}");
            }
        }

        private async Task DoProcessMyModel(ProcessPaylinkOrderMessage message)
        {
            try
            {

                await _payLinkService.PaylinkPaymentAttempts(message.Model.PaylinkId, true);
                
                var settlementResp =
                    await _settlementInfoService.GetSettlementAccountInfo(message.Model.ConsumerSettlementNumber);

                

                var paylinkDetails = await _payLinkService.GetPaylinkDetails(message.Model.PaylinkId);


                var consumerResp = "HubtelSales".Equals(message.Model.IntegrationChannel, StringComparison.OrdinalIgnoreCase)?
                    await _consumerVerificationService.GetConsumerVerificationDetails(message.Model.ConsumerSettlementNumber):
                await _consumerVerificationService.GetConsumerVerificationDetails(paylinkDetails?.PhoneNumber);


                var paylinkTransaction = new PaylinkConsumerTransactionHistory
                {
                    ConsumerId = message.Model.ConsumerSettlementNumber,
                    Id = message.Model.Id,
                    CreatedAt = message.Model.CreatedAt,
                    UpdatedBy = message.Model.UpdatedBy,
                    IntegrationChannel = message.Model.IntegrationChannel,
                    PaylinkId = message.Model.PaylinkId,
                    PaylinkUrl = message.Model.PaylinkUrl,
                    OrderDate = message.Model.OrderDate,
                    OrderNumber = message.Model.OrderNumber,
                    Note = message.Model.Note,
                    Description = message.Model.Description,
                    Status = message.Model.Status,
                    ScenarioType = message.Model.ScenarioType,
                    ResponseCode = message.Model.Payment?.ProviderResponseCode,
                    ProviderResponseCode = message.Model.Payment?.ProviderResponseCode,
                    ProviderName = message.Model.Payment?.PaymentType,
                    ProviderDescription = message.Model.Payment?.ProviderDescription,
                    EmployeeId = message.Model.Payment?.EmployeeId,
                    EmployeeName = message.Model.Payment?.EmployeeName,
                    PayeeMobileNumber = message.Model.CustomerMobileNumber,
                    PayeeEmail = message.Model.CustomerEmail,
                    PayeeName = message.Model.CustomerName,
                    Card = message.Model.Payment?.Card,
                    CardTransactionId = message.Model.Payment?.CardTransactionId,
                    FcmCustomer = message.Model.FcmCustomer,
                    FcmDevice = message.Model.FcmDevice,
                    DiscountAmount = message.Model.DiscountAmount,
                    DiscountRate = message.Model.DiscountRate,
                    SalesDeviceId = message.Model.SalesDeviceId,
                    SalesDeviceType = message.Model.SalesDeviceType,
                    Subtotal = message.Model.Subtotal,
                    TotalAmountDue = message.Model.TotalAmountDue,
                    ReturnUrl = message.Model.ReturnUrl,
                    AmountPaid = message.Model.Payment?.AmountAfterCharges,
                    AmountRefunded = message.Model.AmountRefunded,
                    PaymentType = message.Model.Payment?.PaymentType,
                    Location = message.Model.Location,
                    DeliveryLocation = message.Model.DeliveryLocationName,
                    Currency = message.Model.Currency,
                    LogoUrl = message.Model.LogoUrl,
                    AmountDueCustomer = message.Model.AmountPaid,
                    SenderId = "api".Equals(paylinkDetails?.Source)? paylinkDetails?.SenderId: message.Model.SenderId,
                    CancellationUrl = message.Model.CancellationUrl,
                    PaylinkTitle = message.Model.PaylinkTitle,
                    PaylinkDescription = message.Model.PaylinkDescription,
                    LinkedAccount = settlementResp?.Data?.SettlementType,
                    LinkedAccountId = settlementResp?.Data?.SettlementAccountNumber,
                    LinkedAccountBankCode = settlementResp?.Data?.SettlementBankCode,
                    PaymentReference = $"paylink_{Guid.NewGuid().ToString("N")}",
                    AmountRequested = message.Model.AmountRequested,
                    AggregatedAmount = message.Model.AggregatedAmount,
                    RemainingAmount = message.Model.RemainingAmount,
                    PaylinkUniqueId = message.Model.PaylinkUniqueId,
                    MessageId = message.Model.MessageId,
                    IsPartialPay = message.Model.IsPartialPay,
                    CallbackUrl =  paylinkDetails?.CallbackUrl,
                    ClientReference = paylinkDetails?.ClientReference,
                    Source = paylinkDetails?.Source,
                    IsConsumerVerified = consumerResp?.IsVerified ?? false,
                    SettlementStatus = consumerResp is { IsOnTrustedList: false } ? "untrusted": null
                };

                var res = await _esRepository.Add(paylinkTransaction);
                
                if ("api".Equals(paylinkTransaction.Source,  StringComparison.OrdinalIgnoreCase))
                {
                    var triggerResult = await _kafkaRawProducer.Produce(_kafkaExtra.Value.TriggerCallbackTopic,
                        JsonConvert.SerializeObject(paylinkTransaction));

                    _logger.LogInformation(
                        $"producing trigger callback payload for number: {paylinkTransaction.ConsumerId} ==> " +
                        $"\n payload: {JsonConvert.SerializeObject(paylinkTransaction)} \n " +
                        $"callbackUrl: {message.Model.PaylinkCallbackUrl}\n" +
                        $"payee number: {paylinkTransaction.PayeeMobileNumber} \n" +
                        $"kafka status: {triggerResult.Status}");
                }
                
                var dbDelResult = await _kafkaRawProducer.Produce(_kafkaExtra.Value.PaylinkHistoryDbTopic,
                    JsonConvert.SerializeObject(paylinkTransaction));

                _logger.LogDebug(
                    $"producing data to db for number: {paylinkTransaction.ConsumerId} ==> " +
                    $"\n payload: {JsonConvert.SerializeObject(paylinkTransaction)} \n " +
                    $"callbackUrl: {message.Model.PaylinkCallbackUrl}\n" +
                    $"payee number: {paylinkTransaction.PayeeMobileNumber} \n" +
                    $"kafka status: {dbDelResult.Status}");


                _logger.LogDebug($"for consumer number: {paylinkTransaction.ConsumerId}, \n" +
                                 $"for payee number: {paylinkTransaction.PayeeMobileNumber} \n" +
                                 $"persist response: {res.IsValid} ==> \n" +
                                 $"Debug Information: {res.DebugInformation}");
                
                
                _logger.LogDebug($"Consumer number: {paylinkTransaction.ConsumerId} verification returned: {JsonConvert.SerializeObject(consumerResp)}" +
                                 $"IsConsumerVerified result ==> {paylinkTransaction.IsConsumerVerified}");

                if (paylinkTransaction.IsConsumerVerified)
                {
                    if ("card".Equals(paylinkTransaction.PaymentType, StringComparison.OrdinalIgnoreCase) 
                        && "untrusted".Equals(paylinkTransaction.SettlementStatus, StringComparison.OrdinalIgnoreCase) && paylinkTransaction.AmountPaid >= 1)
                    {
                      _logger.LogInformation("This transaction is a card untrusted transaction greater than 1000, will do nothing... \n" +
                                             $"TransactionId: {paylinkTransaction.Id}\n" +
                                             $"AmountPaid: {paylinkTransaction.AmountPaid}\n" +
                                             $"TransactionType: {paylinkTransaction.PaymentType}\n" +
                                             $"Transaction: {JsonConvert.SerializeObject(paylinkTransaction)}\n");
                    }
                    else
                    {
                        var deliveryResult = await _kafkaRawProducer.Produce(_kafkaExtra.Value.PaylinkTransactionTopic,
                            JsonConvert.SerializeObject(paylinkTransaction));

                        _logger.LogInformation(
                            $"producing payload for number: {paylinkTransaction.ConsumerId} ==> " +
                            $"\n payload: {JsonConvert.SerializeObject(paylinkTransaction)} \n " +
                            $"payee number: {paylinkTransaction.PayeeMobileNumber} \n" +
                            $"kafka status: {deliveryResult.Status}");
                    }
                    
                    // var deliveryResult = await _kafkaRawProducer.Produce(_kafkaExtra.Value.PaylinkTransactionTopic,
                    //     JsonConvert.SerializeObject(paylinkTransaction));
                    //
                    // _logger.LogInformation(
                    //     $"producing payload for number: {paylinkTransaction.ConsumerId} ==> " +
                    //     $"\n payload: {JsonConvert.SerializeObject(paylinkTransaction)} \n " +
                    //     $"payee number: {paylinkTransaction.PayeeMobileNumber} \n" +
                    //     $"kafka status: {deliveryResult.Status}");
                }


                if (!string.IsNullOrEmpty(message.Model.PaylinkUniqueId))
                {
                    var partialPaymentDto = new PartialPaymentRequestDto
                    {
                        AggregatedAmount = message.Model.AggregatedAmount,
                        MessageId = message.Model.MessageId,
                        RemainingAmount = message.Model.RemainingAmount
                    };
                    
                    var del = await _kafkaRawProducer.Produce(_kafkaExtra.Value.MessagingTopic,
                        JsonConvert.SerializeObject(partialPaymentDto));
                    
                    _logger.LogInformation(
                        $"paylink-partial pay for number: {paylinkTransaction.ConsumerId} ==> " +
                        $"\n payload: {JsonConvert.SerializeObject(partialPaymentDto)} \n " +
                        $"payee number: {paylinkTransaction.PayeeMobileNumber} \n" +
                        $"kafka status: {del.Status}");
                }

                
                
            }
            catch (Exception e)
            {
               _logger.LogError($"An error occured processing a payload \n" +
                                $"consumer number: {message.Model.ConsumerSettlementNumber} \n" +
                                $"payee number {message.Model.CustomerMobileNumber} \n" +
                                $"error: {e}");
            }
        }
    }
}