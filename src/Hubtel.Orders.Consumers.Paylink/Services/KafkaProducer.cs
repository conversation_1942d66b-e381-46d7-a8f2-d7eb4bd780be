using System;
using System.Threading.Tasks;
using Confluent.Kafka;
using Hubtel.Orders.Consumers.Paylink.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.Orders.Consumers.Paylink.Services
{
    public class KafkaProducer:IKafkaProducer
    {
        private readonly IOptions<ProducerMessagingKafka> _config;
        private readonly ILogger<KafkaProducer> _logger;
        
        public KafkaProducer(IOptions<ProducerMessagingKafka> config, ILogger<KafkaProducer> logger)
        {
            _config = config;
            _logger = logger;
        }
        
        public async Task Produce<T>(T items, string topic)
        {
            var config = new ProducerConfig{BootstrapServers = _config.Value.BootstrapServers};

            using (var producer = new ProducerBuilder<Null, string>(config).Build())
            {
                await ProduceToTopic(producer, items, topic);
                producer.Flush(TimeSpan.FromSeconds(0.2));
                _logger.LogInformation($"produced to host: {_config.Value.BootstrapServers} with topic{topic} and payload: {JsonConvert.SerializeObject(items)}");
            }
        }
        
        private async Task ProduceToTopic<T>(IProducer<Null, string> producer, T history, string topicName)
        {
            var del =  await producer.ProduceAsync(topicName, new Message<Null, string> { Value= JsonConvert.SerializeObject(history)});
            _logger.LogInformation($"produced to host: {_config.Value.BootstrapServers}\n " +
                                   $"with topic{topicName} and " +
                                   $"\n payload: {JsonConvert.SerializeObject(history)}" +
                                   $"kafka status: {del.Status}");

        }
    }
}