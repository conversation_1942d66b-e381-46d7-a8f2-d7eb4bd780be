using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using Hubtel.Orders.Consumers.Paylink.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Nest;

namespace Hubtel.Orders.Consumers.Paylink.Services
{
    public class EsRepository : IEsRepository
    {
        private readonly IElasticClient _client;
        private readonly IConfiguration _configuration;
        private readonly ILogger<EsRepository> _logger;


        public EsRepository(IElasticClient client, IConfiguration configuration, ILogger<EsRepository> logger)
        {
            _client = client;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<IndexResponse> Add<T>(T doc) where T : class
        {
            return await _client.IndexDocumentAsync(doc);
        }
        
        
        public async Task<IndexResponse> AddFailedIndex<T>(T doc) where T : class
        {
            var asyncIndexResponse = await _client.IndexAsync(new IndexRequest<T>(doc, "failed_paylink_history"));
            return asyncIndexResponse;
        }

        public async Task<BulkResponse> AddBulk<T>(List<T> docs) where T : class
        {
            return await _client.IndexManyAsync(docs);
        }

        public async Task<bool> UpdateStatus(KafkaSendMoneyPaylinkHistory message)
        {
            var bucket = "es_sm_transaction_conversation_id_update";

            try
            {
                dynamic updateFields = new ExpandoObject();
                if (!string.IsNullOrEmpty(message.Status))
                    updateFields.status = message.Status;

                message.Id = message.Id;


                var updateResponse = await _client.UpdateAsync<PaylinkConsumerTransactionHistory, dynamic>(
                    new DocumentPath<PaylinkConsumerTransactionHistory>(message.Id),
                    u => u.Index(_configuration["ElasticSearchConfig:Index"]).Doc(updateFields));

                if (updateResponse.IsValid)
                {
                    return true;
                }

                if (!updateResponse.ServerError.Status.Equals(404))
                    return false;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "conversation id update exception for transaction id {transaction_id}: {exception}",
                    message.Id,
                    e.ToString());

                return false;
            }

            return false;
        }

        public async Task<bool> UpdateSettlementInfoStatus(RmSettlementInfoDto message)
        {
            var bucket = "es_rm_settlement_info_update";

            try
            {
                dynamic updateFields = new ExpandoObject();
                if (!string.IsNullOrEmpty(message.SettlementStatus))
                    updateFields.settlementStatus = message.SettlementStatus;

                if (!string.IsNullOrEmpty(message.SettlementErrorMessage))
                    updateFields.settlementErrorMessage = message.SettlementErrorMessage;
                
                if (!string.IsNullOrEmpty(message.PaymentReference))
                    updateFields.paymentReference = message.PaymentReference;
                
                if (!string.IsNullOrEmpty(message.CallbackDescription))
                    updateFields.callbackDescription = message.CallbackDescription;

                updateFields.settlementDate = message.SettlementDate;


                message.Id = message.Id;


                var updateResponse = await _client.UpdateAsync<PaylinkConsumerTransactionHistory, dynamic>(
                    new DocumentPath<PaylinkConsumerTransactionHistory>(message.Id),
                    u => u.Index(_configuration["ElasticSearchConfig:Index"]).Doc(updateFields));

                if (updateResponse.IsValid)
                {
                    return true;
                }

                if (!updateResponse.ServerError.Status.Equals(404))
                    return false;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "conversation id update exception for transaction id {transaction_id}: {exception}",
                    message.Id,
                    e.ToString());

                return false;
            }

            return false;
        }
        
        public async Task<bool> UpdateSettlementInfoVerifiedStatus(UnverifiedSettlementStatus message)
        {
            var bucket = "es_rm_settlement_info_update";

            try
            {
                dynamic updateFields = new ExpandoObject();
                if (!string.IsNullOrEmpty(message.Status))
                    updateFields.isConsumerVerified = true;

                message.Id = message.Id;


                var updateResponse = await _client.UpdateAsync<PaylinkConsumerTransactionHistory, dynamic>(
                    new DocumentPath<PaylinkConsumerTransactionHistory>(message.Id),
                    u => u.Index(_configuration["ElasticSearchConfig:Index"]).Doc(updateFields));

                if (updateResponse.IsValid)
                {
                    return true;
                }

                if (!updateResponse.ServerError.Status.Equals(404))
                    return false;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "conversation id update exception for transaction id {transaction_id}: {exception}",
                    message.Id,
                    e.ToString());

                return false;
            }

            return false;
        }

        public async Task<PaylinkConsumerTransactionHistory> GetTransactionByField(string key, string fieldName)
        {
            try
            {
                var filters = new List<QueryContainer>
                {
                    new TermQuery { Field = $"{fieldName}.keyword", Value = key },
                };

                var request = new SearchRequest<PaylinkConsumerTransactionHistory>(_configuration["ElasticSearch:Index"])
                {
                    Query = new BoolQuery { Must = filters },
                };

                var response = await _client.SearchAsync<PaylinkConsumerTransactionHistory>(request);
                if (response == null)
                {
                    _logger.LogWarning($"search for transaction id {key} using field name {fieldName} returned null");
                    return null;
                }

                if (response.IsValid)
                {
                    _logger.LogInformation(
                        $"search for transaction id {key} using field name {fieldName} returned valid response");
                    return response.Documents.FirstOrDefault();
                }

                _logger.LogWarning(
                    $"search for transaction id {key} using field name {fieldName} returned invalid response. " +
                    $"debug info is {response.DebugInformation} and server error is {response.ServerError.Error}");

                return null;
            }
            catch (Exception e)
            {
                _logger.LogError(e, $"exception occured for transaction id {key}: {e}");
                return null;
            }
        }
    }
}