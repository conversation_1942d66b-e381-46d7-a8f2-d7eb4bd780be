using System;
using System.Net.Http;
using System.Threading.Tasks;
using Flurl.Http;
using Hubtel.Orders.Consumers.Paylink.Models;
using Hubtel.Orders.Consumers.Paylink.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.Orders.Consumers.Paylink.Services
{
    public interface ISettlementInfoService
    {
        Task<GhqrSettlementInfoResponse> GetSettlementAccountInfo(string number);
    }

    public class SettlementInfoService : ISettlementInfoService
    {
        private readonly ILogger<SettlementInfoService> _logger;
        private readonly GhqrSettlementConfig _ghqrSettlementConfig;

        public SettlementInfoService(ILogger<SettlementInfoService> logger, IOptions<GhqrSettlementConfig> ghqrSettlementConfig)
        {
            _logger = logger;
            _ghqrSettlementConfig = ghqrSettlementConfig.Value;
        }
        
        public async Task<GhqrSettlementInfoResponse> GetSettlementAccountInfo(string number)
        {
            HttpResponseMessage servResp;

            try
            {
                _logger.LogInformation($"Sending Request to {_ghqrSettlementConfig.BaseUrl}/Ghqr?PhoneNumber={number}");
                
                servResp = await $"{_ghqrSettlementConfig.BaseUrl}/Ghqr?PhoneNumber={number}"
                    .AllowAnyHttpStatus().GetAsync();
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occured");
                throw;
            }
            
            var raw = await servResp.Content.ReadAsStringAsync();
            
            _logger.LogInformation($"Raw Response from ghqr settlements: {raw}");

            if (servResp.IsSuccessStatusCode)
            {
                var resp = JsonConvert.DeserializeObject<GhqrSettlementInfoResponse>(raw);

                return resp;
            }
            
            _logger.LogInformation("No settlementAccount Found");

            return null;



        }
    }
}