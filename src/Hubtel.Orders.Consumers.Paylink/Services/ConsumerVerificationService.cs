using System;
using System.Net.Http;
using System.Threading.Tasks;
using Flurl.Http;
using Hubtel.Orders.Consumers.Paylink.Models;
using Hubtel.Orders.Consumers.Paylink.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.Orders.Consumers.Paylink.Services
{
    public interface IConsumerVerificationService
    {
        Task<ConsumerVerificationResp> GetConsumerVerificationDetails(string mobileNumber);
    }

    public class ConsumerVerificationService : IConsumerVerificationService
    {
        private readonly ILogger<ConsumerVerificationService> _logger;
        private readonly IOptions<ConsumerDataConfig> _consumerDataConfig;

        public ConsumerVerificationService(ILogger<ConsumerVerificationService> logger,  IOptions<ConsumerDataConfig> consumerDataConfig)
        {
            _logger = logger;
           _consumerDataConfig = consumerDataConfig;
        }
        
        public async Task<ConsumerVerificationResp> GetConsumerVerificationDetails(string mobileNumber)
        
        {
            var url = $"{_consumerDataConfig.Value.Url}?mobileNumber={mobileNumber}";

            _logger.LogInformation($"starting flurl request to endpoint {url} to get consumer verification details");

            try
            {
                HttpResponseMessage servResp;

                try
                {
                    servResp = await url.WithHeader("Authorization", _consumerDataConfig.Value.Authentication).AllowAnyHttpStatus().GetAsync();
                    
                    _logger.LogInformation($"called endpoint {url} and returned with status {servResp.StatusCode} ");
                }
                catch (Exception e)
                {
                    _logger.LogError(
                        $"error on flurl call to endpoint {url} for consumer verification details: {mobileNumber}, " +
                        $"error: {e}, error message: {e.Message}");

                    return null;
                }

                var raw = await servResp.Content.ReadAsStringAsync();
                _logger.LogInformation(
                    $"flurl response to endpoint {url} returned: {raw}");
                
                
                if (servResp.IsSuccessStatusCode)
                {
                    var resp = JsonConvert.DeserializeObject<ApiResponse<ConsumerVerificationResp>>(raw);

                    return resp.Data;
                }
                
                return null;
            }
            catch (Exception e)
            {
                _logger.LogInformation($"flurl call error {e}, {e.Message}");
                return null;
            }
        }
    }
}