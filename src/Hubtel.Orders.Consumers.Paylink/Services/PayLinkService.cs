using System;
using System.Net.Http;
using System.Threading.Tasks;
using Flurl.Http;
using Hubtel.Orders.Consumers.Paylink.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Hubtel.Orders.Consumers.Paylink.Services
{
    public interface IPayLinkService
    {
        Task<PayLinkDetails> GetPaylinkDetails(string paylinkId);

        Task<bool> PaylinkPaymentAttempts(string paylinkId, bool isSuccess);
    }

    public class PayLinkService : IPayLinkService
    {
        private readonly ILogger<PayLinkService> _logger;
        private readonly string _configUrl;

        public PayLinkService(ILogger<PayLinkService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configUrl = configuration["PayLinkConfig:PayLinkUrl"];
        }

        public async Task<PayLinkDetails> GetPaylinkDetails(string paylinkId)
        {
            var url = _configUrl + $"/PayLinks/{paylinkId}";

            _logger.LogInformation($"starting flurl request to endpoint {url} to get paylink status");

            try
            {
                HttpResponseMessage servResp;

                try
                {
                    servResp = await url.AllowAnyHttpStatus().GetAsync();
                    
                    _logger.LogInformation($"called endpoint {url} and returned with status {servResp.StatusCode} ");
                }
                catch (Exception e)
                {
                    _logger.LogError(
                        $"error on flurl call to endpoint {url} for paylinkId: {paylinkId}, " +
                        $"error: {e}, error message: {e.Message}");

                    return null;
                }

                var raw = await servResp.Content.ReadAsStringAsync();
                _logger.LogInformation(
                    $"flurl response to endpoint {url} returned: {raw}");
                
                
                if (servResp.IsSuccessStatusCode)
                {
                    var resp = JsonConvert.DeserializeObject<ApiResponse<PayLinkDetails>>(raw);

                    return resp.Data;
                }
                
                return null;
            }
            catch (Exception e)
            {
                _logger.LogInformation($"flurl call error {e}, {e.Message}");
                return null;
            }
        }
        
        
        public async Task<bool> PaylinkPaymentAttempts(string paylinkId, bool isSuccess)
        {
            string url;
            if (isSuccess)
            {
                url = _configUrl + $"/PayLinks/{paylinkId}/completed"; 
            }
            else
            {
                url = _configUrl + $"/PayLinks/{paylinkId}/failed"; 
            }
            

            _logger.LogInformation($"starting flurl request to endpoint {url} to get paylink status");

            try
            {
                HttpResponseMessage servResp;

                try
                {
                    servResp = await url.AllowAnyHttpStatus().PatchAsync(null);
                    
                    _logger.LogInformation($"called endpoint {url} and returned with status {servResp.StatusCode} ");
                }
                catch (Exception e)
                {
                    _logger.LogError(
                        $"error on flurl call to endpoint {url} for paylinkId: {paylinkId}, " +
                        $"error: {e}, error message: {e.Message}");

                    return false;
                }

                var raw = await servResp.Content.ReadAsStringAsync();
                _logger.LogInformation(
                    $"flurl response to endpoint {url} returned: {raw}");
                
                
                if (servResp.IsSuccessStatusCode)
                {
                    return true;
                }
                
                return false;
            }
            catch (Exception e)
            {
                _logger.LogInformation($"flurl call error {e}, {e.Message}");
                return false;
            }
        }
    }
}