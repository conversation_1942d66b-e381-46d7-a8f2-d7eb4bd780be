using System.Collections.Generic;
using System.Threading.Tasks;
using Hubtel.Orders.Consumers.Paylink.Models;
using Nest;

namespace Hubtel.Orders.Consumers.Paylink.Services
{
    public interface IEsRepository
    {
        Task<IndexResponse> Add<T>(T doc) where T : class;
        Task<IndexResponse> AddFailedIndex<T>(T doc) where T : class;
        Task<BulkResponse> AddBulk<T>(List<T> docs) where T : class;
        Task<bool> UpdateStatus(KafkaSendMoneyPaylinkHistory message);
        Task<bool> UpdateSettlementInfoStatus(RmSettlementInfoDto message);
        Task<PaylinkConsumerTransactionHistory> GetTransactionByField(string key, string fieldName);
        Task<bool> UpdateSettlementInfoVerifiedStatus(UnverifiedSettlementStatus message);
    }
}