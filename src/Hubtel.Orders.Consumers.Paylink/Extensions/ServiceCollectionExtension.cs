using System;
using Akka.Actor;
using Akka.DI.AutoFac;
using Akka.DI.Core;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Elasticsearch.Net;
using Hubtel.Orders.Consumers.Paylink.Components.Actors;
using Hubtel.Orders.Consumers.Paylink.Options;
using Hubtel.Orders.Consumers.Paylink.Services;
using Microsoft.Extensions.DependencyInjection;
using Nest;
using Nest.JsonNetSerializer;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Hubtel.Orders.Consumers.Paylink.Extensions
{
    public static class ServiceCollectionExtension
    {
        public static IServiceCollection AddActorSystem(this IServiceCollection services, string actorSystemName)
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }


            var actorSystem = ActorSystem.Create(actorSystemName);
            services.AddSingleton(typeof(ActorSystem), sp => actorSystem);

            var builder = new ContainerBuilder();
            builder.Populate(services);

            builder.RegisterType<MainActor>();


            var container = builder.Build();

            var resolver = new AutoFacDependencyResolver(container, actorSystem);

            TopLevelActors.ActorSystem = actorSystem;
            TopLevelActors.MainActor = actorSystem.ActorOf(actorSystem.DI()
                    .Props<MainActor>()
                    .WithSupervisorStrategy(TopLevelActors.GetDefaultSupervisorStrategy)
                , nameof(MainActor));


            return services;
        }


        public static void AddElasticSearch(this IServiceCollection services, ElasticSearchConfig config)
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }

            services.Configure<ElasticSearchConfig>(c =>
            {
                c.Url = config.Url;
                c.Index = config.Index;
            });

            var pool = new SingleNodeConnectionPool(new Uri(config.Url));

            var settings = new ConnectionSettings(pool,
                    sourceSerializer: (builtin, settings) => new MyNewtonsoftJsonNetSerializer(builtin, settings))
                .DefaultIndex(config.Index);

            var client = new ElasticClient(settings);
            var lowLevelClient = new ElasticLowLevelClient(settings);

            services.AddSingleton<IElasticClient>(client);
            services.AddSingleton<IElasticLowLevelClient>(lowLevelClient);
            services.AddSingleton<IEsRepository, EsRepository>();
        }

        public class MyNewtonsoftJsonNetSerializer : ConnectionSettingsAwareSerializerBase
        {
            public MyNewtonsoftJsonNetSerializer(IElasticsearchSerializer builtinSerializer,
                IConnectionSettingsValues connectionSettings)
                : base(builtinSerializer, connectionSettings)
            {
            }

            protected override JsonSerializerSettings CreateJsonSerializerSettings() =>
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Include,
                };

            protected override void ModifyContractResolver(ConnectionSettingsAwareContractResolver resolver) =>
                resolver.NamingStrategy = new CamelCaseNamingStrategy();
        }
    }
}