using System;
using System.Collections.Generic;
using System.Configuration;
using System.Threading.Tasks;
using Gelf.Extensions.Logging;
using Hubtel.Instrumentation.Extensions;
using Hubtel.Kafka.Host;
using Hubtel.Kafka.Host.Core;
using Hubtel.Kafka.Host.Extensions;
using Hubtel.Orders.Consumers.Paylink.Extensions;
using Hubtel.Orders.Consumers.Paylink.Models;
using Hubtel.Orders.Consumers.Paylink.Options;
using Hubtel.Orders.Consumers.Paylink.Services;
using JustEat.StatsD;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Hubtel.Orders.Consumers.Paylink
{
    class Program
    {
        public static async Task Main(string[] args)
        {
            var host = CreateKafkaBuilder(args).Build();
            await host.RunAsync();
        }

        public static IHostBuilder CreateKafkaBuilder(string[] args) =>
            KafkaHost.CreateDefaultBuilder(args)
                .ConfigureServices((hostContext, services) =>
                {
                    //mandatory
                    services.Configure<KafkaConsumerConfig>(options =>
                        hostContext.Configuration.GetSection(nameof(KafkaConsumerConfig)).Bind(options));
                    services.Configure<KafkaProducerConfig>(options =>
                        hostContext.Configuration.GetSection(nameof(KafkaProducerConfig)).Bind(options));
                    
                    services.Configure<ProducerMessagingKafka>(hostContext.Configuration.GetSection("ProducerMessagingKafka"));


                    services.Configure<KafkaExtra>(options =>
                        hostContext.Configuration.GetSection(nameof(KafkaExtra)).Bind(options));
                    
                    services.Configure<ConsumerDataConfig>(options =>
                        hostContext.Configuration.GetSection(nameof(ConsumerDataConfig)).Bind(options));

                    services.Configure<GhqrSettlementConfig>(
                        hostContext.Configuration.GetSection(nameof(GhqrSettlementConfig)));
                    
                    services.Configure<TemplateConfig>(c => hostContext.Configuration.GetSection(nameof(TemplateConfig)).Bind(c));

                    services.AddSingleton<IKafkaProducer, KafkaProducer>();
                    services.AddSingleton<IPayLinkService, PayLinkService>();
                    services.AddSingleton<IConsumerVerificationService, ConsumerVerificationService>();

                    //uncomment these lines for bulk-consume
                    //services.Configure<MessageGroupConsumerLogicConfig>(options => hostContext.Configuration.GetSection(nameof(MessageGroupConsumerLogicConfig)).Bind(options));
                    // services.AddSingleton<IKafkaConsumerLogicBase, MessageGroupConsumerLogic<MyModel>>();

                    //AI
                    services.AddApplicationInsightsTelemetryWorkerService(
                        hostContext.Configuration["ApplicationInsights:InstrumentationKey"]);
                    services.AddApplicationInsightsTelemtryHubtel(
                        hostContext.Configuration["ApplicationInsights:InstrumentationKey"]);

                    services.AddKafkaProducerAgent(hostContext.Configuration["KafkaProducerConfig:BootstrapServers"]);

                    services.AddSingleton<ISettlementInfoService, SettlementInfoService>();


                    //rest of service registry
                    services.AddLogging(loggingBuilder =>
                        loggingBuilder.AddConfiguration(hostContext.Configuration.GetSection("Logging"))
                            .ClearProviders().SetMinimumLevel(LogLevel.Debug).AddConsole()
                            .AddGelf((c) =>
                            {
                                c.AdditionalFields = new Dictionary<string, object>()
                                {
                                    {"facility", hostContext.Configuration.GetSection("Logging")["GELF:Facility"]},
                                    {
                                        "Environment",
                                        hostContext.Configuration.GetSection("Logging")["GELF:Environment"]
                                    },
                                    {"machine_name", Environment.MachineName}
                                };
                                c.Host = hostContext.Configuration.GetSection("Logging")["GELF:Host"];
                                c.LogSource = hostContext.Configuration.GetSection("Logging")["GELF:LogSource"];
                                c.Port = int.Parse(hostContext.Configuration.GetSection("Logging")["GELF:Port"]);
                            }));


                    services.AddStatsD(
                        (provider) =>
                        {
                            var logger = provider.GetService<ILogger<Program>>();
                            return new StatsDConfiguration()
                            {
                                Host = hostContext.Configuration.GetSection("StatsdConfig")["Server"],
                                Port = int.Parse(hostContext.Configuration.GetSection("StatsdConfig")["Port"]),
                                Prefix = hostContext.Configuration.GetSection("StatsdConfig")["Prefix"],

                                OnError = (ex) =>
                                {
                                    logger?.LogError(ex, ex.Message);
                                    return true;
                                }
                            };
                        });

                    services.AddElasticSearch(new ElasticSearchConfig
                    {
                        Index = hostContext.Configuration["ElasticSearchConfig:Index"],
                        Url = hostContext.Configuration["ElasticSearchConfig:Url"]
                    });

                    services.AddActorSystem("MyActorSystem");
                });
    }
}