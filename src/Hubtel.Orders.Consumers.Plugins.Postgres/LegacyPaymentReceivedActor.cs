using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Hubtel.Kafka.Host.Core;
using Hubtel.Orders.Consumers.Common;
using Hubtel.Orders.Consumers.Common.Actors;
using Hubtel.Orders.Consumers.Common.Actors.Messages;
using Hubtel.Orders.Consumers.Common.Models;
using JustEat.StatsD;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Npgsql;
using NpgsqlTypes;

namespace Hubtel.Orders.Consumers.Plugins.Postgres
{
    public class LegacyPaymentReceivedActor:BaseActor
    {
        private readonly IStatsDPublisher _metrics;
        private readonly IKafkaRawProducer _producerService;
        private readonly KafkaExtra _kafkaExtraConfig;
        private readonly DatabaseConnectionString _dbConfig;

        public LegacyPaymentReceivedActor(IOptions<DatabaseConnectionString> dbConfig
            ,IStatsDPublisher metrics
            ,IKafkaRawProducer producerService
            ,IOptions<KafkaExtra> kafkaExtraConfig)
        {
            _metrics = metrics;
            _producerService = producerService;
            _kafkaExtraConfig = kafkaExtraConfig.Value;
            _dbConfig = dbConfig.Value;
            ReceiveAsync<HandleBatchPayments>(async p => await DoHandleBatchPayments(p));
        }

        private async Task DoHandleBatchPayments(HandleBatchPayments message)
        {
            await ProcessPayment(message.Payments.ToDictionary(x => x.OrderId, v => v));
        }

        private async Task ProcessPayment(Dictionary<string, Payment> payments)
        {
            var createdAt = DateTime.Now;

            _metrics.Gauge(payments.Count, "TotalPaymentsDequeued");

            Logger.Debug($"About to process payments");
            bool hasError = false;
            using (var conn = new NpgsqlConnection(_dbConfig.WriterConnection))
            {
                try
                {
                    await conn.OpenAsync();
                    using (_metrics.StartTimer(Buckets.PaymentCreatedSuccessfullyTime.Name))
                    {
                        using (var bulkImporter = conn.BeginBinaryImport(
                            "COPY \"Payments\" (\"Id\", \"BusinessId\", \"CreatedAt\", \"UpdatedAt\", \"CreatedBy\", \"UpdatedBy\", \"PaymentType\"," +
                            " \"OrderId\", \"MomoPhoneNumber\", \"MomoChannel\", \"MomoToken\", \"TransactionId\", \"ExternalTransactionId\"," +
                            " \"AmountAfterCharges\", \"Charges\", \"ChargeCustomer\", \"AmountPaid\", \"PaymentDate\", \"Note\", \"Description\", " +
                            "\"PosDeviceId\", \"PosDeviceType\", \"EmployeeId\", \"EmployeeName\", \"CustomerMobileNumber\", \"CustomerName\", \"BranchId\", " +
                            "\"BranchName\", \"IsRefund\", \"IsSuccessful\", \"ReceiptNumber\", \"Location\", \"Currency\", \"Scheme\", \"Card\", " +
                            "\"Tid\", \"Authorization\", \"Mid\", \"CardTransactionId\", \"AmountTendered\", \"Balance\", \"ClientReference\"," +
                            " \"ProviderDescription\", \"StatusCode\", \"FineractSavingsAccountId\", \"CardTransactionMode\", \"CardProcessor\", " +
                            " \"IsRecurring\", \"RecurringInvoiceId\", \"RefundStatus\", \"RefundDestination\", " +
                            " \"RefundRequestedBy\", \"RefundTransactionId\", \"RefundDestinationType\", \"RefundCompletedDate\", \"RefundRequestedDate\", " +
                            " \"AmountRefunded\")" +
                            "FROM STDIN (FORMAT BINARY)"))
                        {
                            foreach (var record in payments.Values)
                            {
                                bulkImporter.StartRow();
                                bulkImporter.Write(record.Id, NpgsqlDbType.Varchar);
                                bulkImporter.Write(record.BusinessId, NpgsqlDbType.Text);
                                bulkImporter.Write(createdAt, NpgsqlDbType.Timestamp);
                                bulkImporter.WriteNull();
                                bulkImporter.Write(record.CreatedBy, NpgsqlDbType.Text);
                                bulkImporter.Write(record.UpdatedBy, NpgsqlDbType.Text);
                                bulkImporter.Write(record.PaymentType, NpgsqlDbType.Varchar);
                                bulkImporter.Write(record.OrderId, NpgsqlDbType.Varchar);
                                bulkImporter.Write(record.MomoPhoneNumber, NpgsqlDbType.Text);
                                bulkImporter.Write(record.MomoChannel, NpgsqlDbType.Text);
                                bulkImporter.Write(record.MomoToken, NpgsqlDbType.Text);
                                bulkImporter.Write(record.TransactionId, NpgsqlDbType.Varchar);
                                bulkImporter.Write(record.ExternalTransactionId, NpgsqlDbType.Text);
                                bulkImporter.Write(record.AmountAfterCharges, NpgsqlDbType.Numeric);
                                bulkImporter.Write(record.Charges, NpgsqlDbType.Numeric);
                                bulkImporter.Write(record.ChargeCustomer, NpgsqlDbType.Boolean);
                                bulkImporter.Write(record.AmountPaid, NpgsqlDbType.Numeric);
                                bulkImporter.Write(record.PaymentDate, NpgsqlDbType.Timestamp);
                                bulkImporter.Write(record.Note, NpgsqlDbType.Text);
                                bulkImporter.Write(record.Description, NpgsqlDbType.Text);
                                bulkImporter.Write(record.PosDeviceId, NpgsqlDbType.Varchar);
                                bulkImporter.Write(record.PosDeviceType, NpgsqlDbType.Text);
                                bulkImporter.Write(record.EmployeeId, NpgsqlDbType.Varchar);
                                bulkImporter.Write(record.EmployeeName, NpgsqlDbType.Text);
                                bulkImporter.Write(record.CustomerMobileNumber, NpgsqlDbType.Text);
                                bulkImporter.Write(record.CustomerName, NpgsqlDbType.Text);
                                bulkImporter.Write(record.BranchId, NpgsqlDbType.Varchar);
                                bulkImporter.Write(record.BranchName, NpgsqlDbType.Text);
                                bulkImporter.Write(record.IsRefund, NpgsqlDbType.Boolean);
                                bulkImporter.Write(record.IsSuccessful, NpgsqlDbType.Boolean);
                                bulkImporter.Write(record.ReceiptNumber, NpgsqlDbType.Varchar);
                                bulkImporter.Write(record.Location, NpgsqlDbType.Text);
                                bulkImporter.Write(record.Currency, NpgsqlDbType.Varchar);
                                bulkImporter.Write(record.Scheme, NpgsqlDbType.Text);
                                bulkImporter.Write(record.Card, NpgsqlDbType.Text);
                                bulkImporter.Write(record.Tid, NpgsqlDbType.Text);
                                bulkImporter.Write(record.Authorization, NpgsqlDbType.Text);
                                bulkImporter.Write(record.Mid, NpgsqlDbType.Text);
                                bulkImporter.Write(record.CardTransactionId, NpgsqlDbType.Text);
                                bulkImporter.Write(record.AmountTendered, NpgsqlDbType.Numeric);
                                bulkImporter.Write(record.Balance, NpgsqlDbType.Numeric);
                                bulkImporter.Write(record.ClientReference, NpgsqlDbType.Text);
                                bulkImporter.Write(record.ProviderDescription, NpgsqlDbType.Text);
                                bulkImporter.Write(record.StatusCode, NpgsqlDbType.Text);
                                bulkImporter.Write(record.FineractSavingsAccountId, NpgsqlDbType.Integer);
                                bulkImporter.Write(record.CardTransactionMode, NpgsqlDbType.Text);
                                bulkImporter.Write(record.CardProcessor, NpgsqlDbType.Text);
                                bulkImporter.Write(record.IsRecurring, NpgsqlDbType.Boolean);
                                bulkImporter.Write(record.RecurringInvoiceId, NpgsqlDbType.Text);
                                bulkImporter.Write(record.RefundStatus, NpgsqlDbType.Text);
                                bulkImporter.Write(record.RefundDestination, NpgsqlDbType.Text);
                                bulkImporter.Write(record.RefundRequestedBy, NpgsqlDbType.Text);
                                bulkImporter.Write(record.RefundTransactionId, NpgsqlDbType.Text);
                                bulkImporter.Write(record.RefundDestinationType, NpgsqlDbType.Text);
                                bulkImporter.Write(record.RefundCompletedDate, NpgsqlDbType.Date);
                                bulkImporter.Write(record.RefundRequestedDate, NpgsqlDbType.Date);
                                bulkImporter.Write(record.AmountRefunded, NpgsqlDbType.Numeric);
                            }

                            await bulkImporter.CompleteAsync();
                       

                            Logger.Debug($"Persisted bulk payments to database");
                        }
                    }

                    _metrics.Increment(payments.Count, Buckets.PaymentCreatedSuccessfully.Name);
                }
                catch (Exception ex)
                {
                    hasError = true;
                    _metrics.Increment(payments.Count, Buckets.ErrorCount.Name);
                    Logger.Error(ex,
                        $"Error in bulk importer: {ex.Message}. Payments produced to hubtel.sales.order_paid_error");

                }
                finally
                {
                    await conn.CloseAsync();
                }
            }

            if (hasError)
            {
                var topicMessageTuple = payments.Values.Select(p =>
                    Tuple.Create(_kafkaExtraConfig.ErrorTopic, JsonConvert.SerializeObject(p))).ToList();
                //await _producerService.Produce(topicMessageTuple);
                foreach (var tuple in topicMessageTuple)
                {
                    //await _producer.ProduceAsync(tuple.Item1, new Message<Null, string> {Value = tuple.Item2});
                    await _producerService.Produce(tuple.Item1, tuple.Item2);
                }

                _producerService.Flush(TimeSpan.FromMilliseconds(10));
                return;
            }
            var newPayments = await UpdatePaymentFields(payments, createdAt);
            if (newPayments != null && newPayments.Count > 0)
            {
                await UpdateOrderStatuses(newPayments);
                await FetchOrdersAndPushToKafka(newPayments);
            }
        }

        /// <summary>
        /// The query below allows us to  ensure that only the latest added payments are updated and returned
        /// for further processing
        /// </summary>
        /// <param name="payments"></param>
        /// <param name="createdAt"></param>
        /// <returns></returns>
        private async Task<List<Payment>> UpdatePaymentFields(Dictionary<string, Payment> payments, DateTime createdAt)
        {
            var newPayments = new List<Payment>();

            Logger.Debug($"About to update extra fields for payments");
            if (payments.Count == 0)
            {
                return newPayments;
            }

            //get records here update and put inside temp table
            using (var conn = new NpgsqlConnection(_dbConfig.WriterConnection))
            {
                await conn.OpenAsync();
                try
                {
                    var orderIds = string.Join(",", payments.Keys.Select(x => $"('{x}')").ToList());

                    using (var cmd = new NpgsqlCommand())
                    {
                        cmd.Connection = conn;
                        cmd.CommandText =
                            $"UPDATE \"Payments_{createdAt.ToString("yyyy_MM_dd")}\" P SET \"BranchId\" = O.\"BranchId\", \"BranchName\" = O.\"BranchName\", " +
                            $" \"IsRecurring\" = O.\"IsRecurring\", \"RecurringInvoiceId\" =  O.\"RecurringInvoiceId\" , \"EmployeeId\" = O.\"EmployeeId\"," +
                            $" \"EmployeeName\" = O.\"EmployeeName\", " +
                            $"\"Location\" = O.\"Location\",\"DeliveryFee\" = O.\"DeliveryFee\" FROM \"Orders\" O WHERE P.\"OrderId\" = O.\"Id\" AND P.\"OrderId\" = ANY(VALUES" +
                            orderIds + ") " +
                            $"{PartitionedQueryHelpers.OrderPaymentsConstraintQuery()}  RETURNING *;";
                        Logger.Debug($"query after conditional added {cmd.CommandText}");
                        using (_metrics.StartTimer(Buckets.UpdatePaymentInformationFromOrders.Name))
                        {
                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    var orderId = reader.GetString(7);
                                    newPayments.Add(payments[orderId]);
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _metrics.Increment(payments.Count, Buckets.ErrorCount.Name);
                    Logger.Error($"Couldn't update extra payment fields {ex.Message}:", ex);
                }
                finally
                {

                   await conn.CloseAsync();
                }

            }

            return newPayments;
        }

        //need status
        private async Task UpdateOrderStatuses(List<Payment> payments)
        {
            Logger.Debug($"About to update order status for payments");
            if (payments.Count == 0)
            {
                return;
            }

            //get records here update and put inside temp table
            using (var conn = new NpgsqlConnection(_dbConfig.WriterConnection))
            {
                await conn.OpenAsync();
                try
                {
                    using (var cmd = new NpgsqlCommand())
                    {
                        cmd.Connection = conn;
                        cmd.CommandText =
                            $"CREATE TEMPORARY TABLE IF NOT EXISTS \"TempPayments_{UniqueAppId.GetId()}\"(\"OrderId\" character varying(256),\"AmountPaid\" numeric(20,4),\"IsSuccessful\" boolean NOT NULL);";
                        using (_metrics.StartTimer(Buckets.CreateTemporaryTable.Name))
                        {
                            await cmd.ExecuteNonQueryAsync();
                        }
                    }

                    using (_metrics.StartTimer(Buckets.TemporaryTableBulkInsertTime.Name))
                    {
                        using (var bulkImporter = conn.BeginBinaryImport(
                            $"COPY \"TempPayments_{UniqueAppId.GetId()}\" (\"OrderId\", \"AmountPaid\", \"IsSuccessful\") FROM STDIN (FORMAT BINARY)")
                        )
                        {
                            foreach (var record in payments)
                            {
                                bulkImporter.StartRow();
                                bulkImporter.Write(record.OrderId, NpgsqlDbType.Varchar);
                                bulkImporter.Write(record.AmountPaid, NpgsqlDbType.Numeric);
                                bulkImporter.Write(record.IsSuccessful, NpgsqlDbType.Boolean);
                            }

                            await bulkImporter.CompleteAsync();
                        }
                    }

                    using (var cmd = new NpgsqlCommand())
                    {
                        cmd.Connection = conn;
                        cmd.CommandText =
                            $"UPDATE \"Orders\" O SET \"AmountPaid\" = CASE WHEN TP.\"IsSuccessful\"=true THEN " +
                            $"TP.\"AmountPaid\" ELSE 0 END, \"Status\" = CASE WHEN TP.\"IsSuccessful\"=true THEN 'Paid'" +
                            $" ELSE 'Unpaid' END FROM \"TempPayments_{UniqueAppId.GetId()}\" TP" +
                            $" WHERE O.\"Id\" = TP.\"OrderId\" {PartitionedQueryHelpers.OrderConstraintQuery()};";
                        Logger.Debug($"query after conditional added {cmd.CommandText}");
                        using (_metrics.StartTimer(Buckets.BulkUpdateOnOrders.Name))
                        {
                            await cmd.ExecuteNonQueryAsync();
                        }
                    }
                }
                catch (Exception ex)
                {
                    _metrics.Increment(payments.Count, Buckets.ErrorCount.Name);
                    Logger.Error(ex, $"Couldn't update order statuses {ex.Message}:");
                }
                finally
                {
                    await conn.CloseAsync();
                }

                
            }

            //update with table
        }

        private async Task FetchOrdersAndPushToKafka(List<Payment> payments)
        {
            Logger.Debug($"About to fetch orders to push to kafka");
            if (payments.Count == 0)
            {
                return;
            }

            Dictionary<string, Order> orders = new Dictionary<string, Order>();
            bool hasError = false;
            var orderIds = string.Join(",", payments.Select(x => $"('{x.OrderId}')").ToList());
            using (var conn = new NpgsqlConnection(_dbConfig.ReaderConnection))
            {
            
                try
                {
                    await conn.OpenAsync();

                    using (var cmd = new NpgsqlCommand())
                    {
                        cmd.Connection = conn;
                        cmd.CommandText =
                            "SELECT O.\"Id\", O.\"BusinessId\", coalesce(\"IntegrationChannel\",''), \"OrderDate\", coalesce(\"OrderNumber\",''), coalesce(O.\"Note\",''), " +
                            "coalesce(O.\"Description\",''), coalesce(\"Status\",''), coalesce(\"EmployeeId\",''), coalesce(\"EmployeeName\",''), coalesce(\"CustomerMobileNumber\",''), coalesce(\"CustomerName\",''), coalesce(\"BranchId\",''), " +
                            "coalesce(\"BranchName\",''), coalesce(O.\"DiscountRate\", 0), coalesce(O.\"DiscountAmount\", 0), coalesce(\"Subtotal\", 0), coalesce(\"TotalAmountDue\", 0), " +
                            "coalesce(\"AmountPaid\", 0), coalesce(\"AmountRefunded\", 0), coalesce(\"PaymentTypes\",''), coalesce(\"Location\",''), coalesce(\"Currency\",''), \"IsFulfilled\", " +
                            "coalesce(\"ConsumerFeedback\",''), coalesce(\"ConsumerRating\", 0), coalesce(\"BusinessEmail\",''), coalesce(\"BusinessMobileNumber\",''), coalesce(\"CustomerEmail\",''), coalesce(\"FcmCustomer\",''), " +
                            "coalesce(\"FcmDevice\",''), \"AmountDueProducer\", \"DeliveryFee\", \"HasDelivery\", coalesce(\"BranchEmail\",''), coalesce(\"BranchPhoneNumber\",''), coalesce(\"BusinessName\",''), " +
                            "\"CustomerReward\", coalesce(\"SenderId\",''), coalesce(OI.\"ItemId\",''), coalesce(OI.\"Name\",''), coalesce(OI.\"Quantity\", 0), coalesce(OI.\"ServiceRequestId\",''), coalesce(OI.\"UnitPrice\",0),coalesce(OI.\"ServiceData\",'')," +
                            " coalesce(O.\"ReturnUrl\",''), coalesce(O.\"LogoUrl\",''),coalesce(O.\"DeliveryLocationName\",''), \"IsRecurring\" " +
                            ",coalesce(O.\"RecurringInvoiceId\",''), \"IsProgrammableService\" , coalesce(O.\"AssignedTo\",'') FROM public.\"Orders\" O " +
                            "JOIN public.\"OrderItems\" OI ON O.\"Id\"=OI.\"OrderId\" WHERE O.\"Id\" = ANY(VALUES" +
                            orderIds + ") " +
                            $" {PartitionedQueryHelpers.OrderAndOrderItemsConstraintQuery()};";
                        Logger.Debug($"query after conditional added {cmd.CommandText}");
                        using (_metrics.StartTimer(Buckets.FetchOrderAndOrderItemsTime.Name))
                        {
                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    var orderId = reader.GetString(0);

                                    if (orders.ContainsKey(orderId))
                                    {
                                        var orderItem = new OrderItem();
                                        orderItem.ItemId = reader.GetString(39);
                                        orderItem.Name = reader.GetString(40);
                                        orderItem.Quantity = reader.GetInt32(41);
                                        orderItem.ServiceRequestId = reader.GetString(42);
                                        orderItem.UnitPrice = reader.GetDecimal(43);
                                        orderItem.ServiceData = reader.GetString(44);
                                        orders[orderId].OrderItems.Add(orderItem);
                                    }
                                    else
                                    {
                                        var order = new Order();
                                        order.Id = orderId;
                                        order.BusinessId = reader.GetString(1);
                                        order.IntegrationChannel = reader.GetString(2);
                                        order.OrderDate = reader.GetDateTime(3);
                                        order.OrderNumber = reader.GetString(4);
                                        order.Note = reader.GetString(5);
                                        order.Description = reader.GetString(6);
                                        order.Status = reader.GetString(7);
                                        order.EmployeeId = reader.GetString(8);
                                        order.EmployeeName = reader.GetString(9);
                                        order.CustomerMobileNumber = reader.GetString(10);
                                        order.CustomerName = reader.GetString(11);
                                        order.BranchId = reader.GetString(12);
                                        order.BranchName = reader.GetString(13);
                                        order.DiscountRate = reader.GetFloat(14);
                                        order.DiscountAmount = reader.GetDecimal(15);
                                        order.Subtotal = reader.GetDecimal(16);
                                        order.TotalAmountDue = reader.GetDecimal(17);
                                        order.AmountPaid = reader.GetDecimal(18);
                                        order.AmountRefunded = reader.GetDecimal(19);
                                        order.PaymentTypes = reader.GetString(20);
                                        order.Location = reader.GetString(21);
                                        order.Currency = reader.GetString(22);
                                        //order.IsFulfilled = reader.GetBoolean(23);
                                        //order.IsFulfilled = (bool?)reader[23];
                                        order.IsFulfilled = reader.GetFieldValue<bool?>(23);
                                        order.ConsumerFeedback = reader.GetString(24);
                                        order.ConsumerRating = reader.GetInt32(25);
                                        order.BusinessEmail = reader.GetString(26);
                                        order.BusinessMobileNumber = reader.GetString(27);
                                        order.CustomerEmail = reader.GetString(28);
                                        order.FcmCustomer = reader.GetString(29);
                                        order.FcmDevice = reader.GetString(30);
                                        order.AmountDueProducer = reader.GetDecimal(31);
                                        order.DeliveryFee = reader.GetDecimal(32);
                                        order.HasDelivery = reader.GetBoolean(33);
                                        order.BranchEmail = reader.GetString(34);
                                        order.BranchPhoneNumber = reader.GetString(35);
                                        order.BusinessName = reader.GetString(36);
                                        order.CustomerReward = reader.GetDouble(37);
                                        order.SenderId = reader.GetString(38);


                                        if (orders.TryAdd(orderId, order))
                                        {
                                            var orderItem = new OrderItem();
                                            orderItem.ItemId = reader.GetString(39);
                                            orderItem.Name = reader.GetString(40);
                                            orderItem.Quantity = reader.GetInt32(41);
                                            orderItem.ServiceRequestId = reader.GetString(42);
                                            orderItem.UnitPrice = reader.GetDecimal(43);
                                            orderItem.ServiceData = reader.GetString(44);

                                            if (orders[orderId].OrderItems == null)
                                                orders[orderId].OrderItems = new List<OrderItem>();
                                            orders[orderId].OrderItems.Add(orderItem);
                                        }

                                        order.ReturnUrl = reader.GetString(45);
                                        order.LogoUrl = reader.GetString(46);
                                        order.DeliveryLocationName = reader.GetString(47);
                                        order.IsRecurring = reader.GetBoolean(48);
                                        order.RecurringInvoiceId = reader.GetString(49);
                                        order.IsProgrammableService = reader.GetBoolean(50);
                                        order.AssignedTo = reader.GetString(51);
                                    }

                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _metrics.Increment(payments.Count, Buckets.ErrorCount.Name);
                    Logger.Error(ex, $"Couldn't fetch orders to push to kafka {ex.Message}:");
                    // conn.Close();
                    // return;
                    hasError = true;
                }
                finally
                {
                    await conn.CloseAsync();
                }
              
            }

            if (hasError)
            {
                return;
            }

            await GetInvoiceAdditionInformation(orderIds, orders);
            await PushOrdersToKafka(orders, payments);
        }

        private async Task GetInvoiceAdditionInformation(string orderIds, Dictionary<string, Order> orders)
        {
            using (var conn = new NpgsqlConnection(_dbConfig.ReaderConnection))
            {

                try
                {
                    await conn.OpenAsync();

                    var invoiceAdditions = new List<InvoiceAddition>();
                    using (var cmd = new NpgsqlCommand())
                    {
                        cmd.Connection = conn;
                        cmd.CommandText =
                            "SELECT \"Id\",  coalesce(\"BusinessId\",''), \"OrderId\", \"BranchId\", \"IsInclusive\", \"Sequence\", " +
                            "\"Name\", \"IsFlatFee\", \"Figure\", \"CalculationMethod\", \"IsActive\"," +
                            "  coalesce(\"ComputedValue\",0) FROM public.\"OrderInvoiceAdditions\" WHERE \"OrderId\" = ANY(VALUES" +
                            orderIds + ") ;";
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                //use object assignment to know which line cause an issue if there's one
                                var invoiceAddition = new InvoiceAddition();
                                invoiceAddition.Id = reader.GetString(0);
                                invoiceAddition.BusinessId = reader.GetString(1);
                                invoiceAddition.OrderId = reader.GetString(2);
                                invoiceAddition.BranchId = reader.GetString(3);
                                invoiceAddition.IsInclusive = reader.GetBoolean(4);
                                invoiceAddition.Sequence = reader.GetInt16(5);
                                invoiceAddition.Name = reader.GetString(6);
                                invoiceAddition.IsFlatFee = reader.GetBoolean(7);
                                invoiceAddition.Figure = reader.GetDecimal(8);
                                invoiceAddition.CalculationMethod = reader.GetString(9);
                                invoiceAddition.IsActive = reader.GetBoolean(10);
                                invoiceAddition.ComputedValue = reader.GetDecimal(11);
                                invoiceAdditions.Add(invoiceAddition);
                            }
                        }

                        var orderedInvoiceAdditions = invoiceAdditions.OrderBy(x => x.OrderId).ToList();
                        for (var i = 0; i < invoiceAdditions.Count; i++)
                        {
                            try
                            {
                                if (orders.TryGetValue(orderedInvoiceAdditions[i].OrderId, out Order order))
                                {
                                    var additions = orderedInvoiceAdditions
                                        .Where(x => x.OrderId.Equals(orderedInvoiceAdditions[i].OrderId)).ToList();
                                    order.InvoiceAdditions = additions;
                                    i = i + additions.Count;
                                    //FEATURE:: skip if you have found match since its already ordered

                                }
                            }
                            catch (ArgumentNullException)
                            {
                                Logger.Error($"Null order id for {JsonConvert.SerializeObject(invoiceAdditions[i])}");
                            }

                        }

                        Logger.Debug($"query after conditional added {cmd.CommandText}");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, $"Couldn't fetch invoice stuff {ex.Message}:");
                }
                finally
                {
                    await conn.CloseAsync();
                }
            }
        }

        private async Task PushOrdersToKafka(Dictionary<string, Order> orders, List<Payment> payments)
        {
            var paidOrders = new List<Order>();
            var paidAndUnpaid = new List<Order>();
            var programmableOrders = new List<Order>();
            var cnt = payments.Count;
            for (int i = 0; i < cnt; i++)
            {
                if (payments[i].IsSuccessful)
                {
                    if (orders.TryGetValue(payments[i].OrderId, out Order order))
                    {
                        order.Payment = payments[i];
                        if (!order.IsProgrammableService)
                        {
                            paidOrders.Add(order);
                        }
                        else
                        {
                            programmableOrders.Add(order);
                        }

                        paidAndUnpaid.Add(order);
                    }
                }
                else
                {
                    if (orders.TryGetValue(payments[i].OrderId, out Order order))
                    {
                        order.Payment = payments[i];
                        paidAndUnpaid.Add(order);
                    }
                }

            }


            if (paidOrders.Any())
            {
                _metrics.Increment(paidOrders.Count, Buckets.PaidOrders.Name);
                Logger.Debug($"Pushing paid orders to kafka");

                _metrics.Increment(orders.Count, Buckets.OrderPaidPaymentPublishBucket.Name);

                var topicMessageTuple = paidOrders.Select(p =>
                    Tuple.Create(_kafkaExtraConfig.ProducePaidTopic, JsonConvert.SerializeObject(p))).ToList();
                //await _producerService.Produce(topicMessageTuple);

                foreach (var tuple in topicMessageTuple)
                {
                    //await _producer.ProduceAsync(tuple.Item1, new Message<Null, string> {Value = tuple.Item2});
                    await _producerService.Produce(tuple.Item1, tuple.Item2);
                }

                _producerService.Flush(TimeSpan.FromMilliseconds(10));

            }

            if (paidAndUnpaid.Any())
            {
                _metrics.Increment(orders.Count, Buckets.OrderPaidPaymentPublishBucket.Name);

                var topicMessageTuple = paidAndUnpaid.Select(p =>
                    Tuple.Create(_kafkaExtraConfig.ProducePaidAndUnpaidTopic, JsonConvert.SerializeObject(p))).ToList();

                _metrics.Increment(paidAndUnpaid.Count, Buckets.OrderPaidAndUnpaidPaymentPublishBucket.Name);
                //await _producerService.Produce(topicMessageTuple);
                foreach (var tuple in topicMessageTuple)
                {
                    //await _producer.ProduceAsync(tuple.Item1, new Message<Null, string> {Value = tuple.Item2});
                    await _producerService.Produce(tuple.Item1, tuple.Item2);
                }

                _producerService.Flush(TimeSpan.FromMilliseconds(10));

            }

            if (programmableOrders.Any())
            {

                var topicMessageTuple = programmableOrders.Select(p =>
                    Tuple.Create(_kafkaExtraConfig.ProducePaidProgrammableTopic, JsonConvert.SerializeObject(p))).ToList();

                _metrics.Increment(programmableOrders.Count, Buckets.OrderPaidProgrammableService.Name);
                //await _producerService.Produce(topicMessageTuple);
                foreach (var tuple in topicMessageTuple)
                {
                    //await _producer.ProduceAsync(tuple.Item1, new Message<Null, string> {Value = tuple.Item2});
                    await _producerService.Produce(tuple.Item1, tuple.Item2);
                }

                _producerService.Flush(TimeSpan.FromMilliseconds(10));

            }
        }
    }
}
