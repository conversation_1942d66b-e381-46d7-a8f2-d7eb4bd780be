using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.DI.Core;
using Akka.Streams.Implementation.Fusing;
using Akka.Util.Internal;
using AutoMapper;
using Confluent.Kafka;
using Flurl.Http;
using Hubtel.Instrumentation;
using Hubtel.Instrumentation.Redis;
using Hubtel.Kafka.Host.Core;
using Hubtel.Orders.Consumers.Common;
using Hubtel.Orders.Consumers.Common.Actors;
using Hubtel.Orders.Consumers.Common.Actors.Messages;
using Hubtel.Orders.Consumers.Common.Models;
using Hubtel.Orders.Consumers.Plugins.Postgres.Messages;
using Hubtel.Orders.Consumers.Storage.Pg;
using Hubtel.Orders.Consumers.Storage.Pg.Entities;
using Hubtel.Redis.Sdk;
using Hubtel.Redis.Sdk.Options;
using JustEat.StatsD;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Npgsql;
using NpgsqlTypes;
using Order = Hubtel.Orders.Consumers.Common.Models.Order;

namespace Hubtel.Orders.Consumers.Plugins.Postgres
{
    public class PaymentReceivedActor : BaseActor
    {
        private readonly IStatsDPublisher _metrics;
        private readonly IKafkaRawProducer _producerService;

        private readonly ICacheManager _cacheManager;

        // private readonly IRedisConnectionFactory _redisConnectionFactory;
        private readonly IGenericRepository _pgRepository;
        private readonly IHubtelDiagnosticsPublisher _aiPublisher;
        private readonly KafkaProducerConfig _producerConfig;
        private readonly IMapper _mapper;
        private readonly IPaymentReceivedDbOperation _paymentReceivedDbOperation;
        private readonly EsConfig _esConfig;
        private readonly RedisConfiguration _redisConfig;
        private readonly IOptions<RedisConfiguration> _redisOptions;
        private readonly KafkaExtra _kafkaExtraConfig;
        private readonly IOptions<MultiCartPaymentConfig> _multiCartConfig;
        private readonly IOptions<CardVerficationConfig> _cardVerificationConfig;


        public PaymentReceivedActor(IStatsDPublisher metrics
            , IKafkaRawProducer producerService
            , IOptions<KafkaExtra> kafkaExtraConfig
            , ICacheManager cacheManager
            // ,IRedisConnectionFactory redisConnectionFactory
            , IOptions<RedisConfiguration> redisConfig
            , IGenericRepository pgRepository
            , IHubtelDiagnosticsPublisher aiPublisher
            , IOptions<KafkaProducerConfig> producerConfig
            , IMapper mapper
            , IPaymentReceivedDbOperation paymentReceivedDbOperation
            , IOptions<EsConfig> esConfig, IOptions<MultiCartPaymentConfig> multiCartConfig, IOptions<CardVerficationConfig> cardVerificationConfig)
        {
            _metrics = metrics;
            _producerService = producerService;
            _cacheManager = cacheManager;
            //   _redisConnectionFactory = redisConnectionFactory;
            _pgRepository = pgRepository;
            _aiPublisher = aiPublisher;
            _producerConfig = producerConfig.Value;
            _mapper = mapper;
            _paymentReceivedDbOperation = paymentReceivedDbOperation;
            _multiCartConfig = multiCartConfig;
            _cardVerificationConfig = cardVerificationConfig;
            _esConfig = esConfig.Value;
            _redisConfig = redisConfig.Value;
            _redisOptions = redisConfig;
            _kafkaExtraConfig = kafkaExtraConfig.Value;


            ReceiveAsync<HandleBatchPayments>(async p => await DoHandleBatchPayments(p));
        }


        private async Task DoHandleBatchPayments(HandleBatchPayments message)
        {
            Logger.Debug($"received request to process {message.Payments.Count} payments");

            var orderTodbList = new HashSet<Order>();


            foreach (var payment in message.Payments)
            {
                var order = await GetOrderPayload(payment.OrderId);

                if (order != null)
                {
                    var p = payment;
                    order.Status = p.IsSuccessful ? "Paid" : "Unpaid";

                    order.InvoiceAdditions.ForEach(ia => ia.Id = Guid.NewGuid().ToString("N"));

                    order.OrderItems.ForEach(a => a.OrderId = order.Id);

                    order.OrderDate = order.CreatedAt;
                    order.AmountPaid = p.AmountPaid;

                    p.BranchId = order.BranchId;
                    p.DeliveryFee = order.DeliveryFee;
                    p.IsRecurring = order.IsRecurring;
                    p.RecurringInvoiceId = order.RecurringInvoiceId;
                    p.BranchName = order.BranchName;
                    p.EmployeeId = order.EmployeeId;
                    p.EmployeeName = order.EmployeeName;
                    p.Location = order.Location;
                    p.PaymentProcessor = order.PaymentRequest?.PaymentProcessorName;
                    p.PaymentDate =  !string.Equals(order.PaymentRequest?.Channel, "bankpay", StringComparison.OrdinalIgnoreCase) ?
                        order.CreatedAt : p.PaymentDate; 
                    p.PaymentCategory = order.PaymentRequest?.PaymentCategory;
                    p.Tips =order.PaymentRequest.Tips;
                    p.TipSettlement = order.PaymentRequest.TipSettlement;
                    p.HasTip = order.PaymentRequest.HasTip;
                    p.AmountPlusTips = order.PaymentRequest.AmountPlusTips;
                    p.ChargeCustomer = order.PaymentRequest?.FeesOnCustomer ?? false;
                    p.ElevyAmount = order.InstantServiceDetails?.ElevyAmount != null?
                    decimal.Parse(order.InstantServiceDetails?.ElevyAmount) : decimal.Zero;
                    p.IntegrationChannel = order.IntegrationChannel;
                    p.TaxWithHolding = order.TaxWithHolding;
                    p.IsBulkSettlement = order.IsBulkSettlement;

                    order.Payment = p;


                    if (order.IsMultiCart)
                    {
                        await Task.Delay(50);
                        var i = 1;
                        foreach (var id in order.MultiCartIds)
                        {
                            var multiCartOrder = await GetOrderPayload(id.Value);

                            if (multiCartOrder == null)
                            {
                                continue;
                            }

                            multiCartOrder.Status = p.IsSuccessful ? "Paid" : "Unpaid";

                            multiCartOrder.InvoiceAdditions.ForEach(ia => ia.Id = Guid.NewGuid().ToString("N"));

                            multiCartOrder.OrderItems.ForEach(a => a.OrderId = multiCartOrder.Id);

                            multiCartOrder.OrderDate = multiCartOrder.CreatedAt;
                            multiCartOrder.AmountPaid = multiCartOrder.PaymentRequest.Amount;

                            multiCartOrder.Payment = new Payment
                            {
                                BranchId = multiCartOrder.BranchId,
                                BranchName = multiCartOrder.BranchName,
                                FineractSavingsAccountId = multiCartOrder.PaymentRequest.SavingsAccountId,
                                OrderId = multiCartOrder.Id,
                                AmountPaid = multiCartOrder.PaymentRequest.Amount,
                                AmountAfterCharges = multiCartOrder.PaymentRequest.AmountAfterCharges,
                                Charges = multiCartOrder.PaymentRequest.Fee,
                                Description = multiCartOrder.PaymentRequest.Description,
                                DeliveryFee = multiCartOrder.PaymentRequest.DeliveryFee,
                                BusinessId = multiCartOrder.PaymentRequest.BusinessId,
                                EmployeeId = multiCartOrder.EmployeeId,
                                EmployeeName = multiCartOrder.EmployeeName,
                                PaymentType = multiCartOrder.PaymentRequest.PaymentType,
                                PaymentDate = multiCartOrder.CreatedAt,
                                CreatedAt = payment.CreatedAt,
                                IsSuccessful = p.IsSuccessful,
                                Id = p.Id,
                                ReceiptNumber = multiCartOrder.PaymentRequest.ReceiptNumber,
                                PaymentProcessor = multiCartOrder.PaymentRequest.PaymentProcessorRoute,
                                RefundStatus = multiCartOrder.PaymentRequest.RefundStatus,
                                StatusCode = p.StatusCode,
                                ProviderDescription = p.ProviderDescription,
                                ProviderResponseCode = p.ProviderResponseCode,
                                ClientReference = p.ClientReference,
                                Balance = p.Balance,
                                AmountTendered = p.AmountTendered,
                                CardTransactionId = p.CardTransactionId,
                                Mid = p.Mid,
                                Authorization = p.Authorization,
                                Tid = p.Tid,
                                Card = p.Card,
                                Currency = p.Currency,
                                Location = p.Location,
                                IsRefund = p.IsRefund,
                                MomoPhoneNumber = p.MomoPhoneNumber,
                                MomoChannel = p.MomoChannel,
                                TransactionId = p.TransactionId,
                                ExternalTransactionId = p.ExternalTransactionId,
                                ChargeCustomer = p.ChargeCustomer,
                                Note = p.Note,
                                PosDeviceId = p.PosDeviceId,
                                PosDeviceType = p.PosDeviceType,
                                CustomerMobileNumber = p.CustomerMobileNumber,
                                CustomerName = p.CustomerName,
                                RefundDestination = p.RefundDestination,
                                RefundCompletedDate = p.RefundCompletedDate,
                                RefundRequestedBy = p.RefundRequestedBy,
                                RefundDestinationType = p.RefundDestinationType,
                                RefundRequestedDate = p.RefundRequestedDate,
                                RecurringInvoiceId = p.RecurringInvoiceId,
                                RefundTransactionId = p.RefundTransactionId,
                                AmountRefunded = p.AmountRefunded,
                                CallbackUrl = p.CallbackUrl,
                                CardProcessor = p.CardProcessor,
                                CreatedBy = p.CreatedBy,
                                UpdatedAt = p.UpdatedAt,
                                UpdatedBy = p.UpdatedBy
                            };


                            orderTodbList.Add(multiCartOrder);
                        }

                        order.Payment.PaymentType = _multiCartConfig.Value.MultiCartPaymentType;
                    }

                    orderTodbList.Add(order);
                }
            }


            if (!orderTodbList.Any())
            {
                Logger.Warning($"nothing found in redis for {message.Payments.Count} payment_received items");
                return;
            }
            await DoPersistOperations(orderTodbList.ToList());
        }

        public async Task<Order> GetOrderPayload(string orderId)
        {
            await Task.Delay(2);
            var orderPayload = await _cacheManager.Get(orderId);
            Order order = null;
            if (string.IsNullOrEmpty(orderPayload))
            {
                Logger.Debug("could not find transaction with order ID  {order_id} in orders redis. Will try ES",
                    orderId);
                try
                {
                    var esResp = await $"{_esConfig.Url}{orderId}".AllowAnyHttpStatus().GetAsync();


                    var json = await esResp.Content.ReadAsStringAsync();

                    var esOrder = JsonConvert.DeserializeObject<EsOrder>(json);
                    Logger.Debug($"es order: {json} found in ES");


                    if (!esOrder.Found)
                    {
                        Logger.Warning("could not find transaction order ID {order_id} in ES. What do we do here?.",
                            orderId);

                        var orderRedis = await _cacheManager.Get(orderId);

                        order = JsonConvert.DeserializeObject<Order>(orderRedis);
                        return order;
                    }

                    Logger.Debug($"order id: {orderId} found in ES. Will continue flow");
                    order = esOrder.Source;
                }
                catch (Exception e)
                {
                    Logger.Error(e, e.Message);
                }
            }
            else
            {
                Logger.Debug("found transaction with order ID  {order_id} in orders redis",
                    orderId);
                order = JsonConvert.DeserializeObject<Order>(orderPayload);
            }


            return order;
        }

        private async Task DoPersistOperations(List<Order> orderTodbList)
        {
            Logger.Debug("about to persist to kafka");
            await PublishToKafka(orderTodbList);
            Logger.Info("about to remove payment from redis");
            await PopFromRedis(orderTodbList);
        }

        private async Task PublishToKafka(List<Order> orders)
        {
            var actor = Context.ActorOf(
                Props.Create(() => new KafkaActor(_producerService, _metrics, _kafkaExtraConfig)),
                $"KafkaActor{Guid.NewGuid():N}");

            actor.Tell(new ProduceToKafka(orders));
            await Task.Delay(0);
        }

        private async Task PopFromRedis(List<Order> orders)
        {
            var actor = Context.ActorOf(
                Props.Create(() => new DbPersistActor(_pgRepository, _cacheManager, _redisOptions, _aiPublisher,
                    _paymentReceivedDbOperation)),
                $"RedisCleanupPersistor{Guid.NewGuid():N}");


            actor.Tell(new CleanupRedis(orders));
            await Task.Delay(0);
        }

        private Order PrepareOrderForPgInsert(string orderPayload, List<Payment> payments)
        {
            try
            {
                if (string.IsNullOrEmpty(orderPayload))
                {
                    Logger.Debug(
                        $"empty result from redis for order id one of these order IDs {string.Join(",", payments.Select(p => p.OrderId))}");
                    return null;
                }

                var order = JsonConvert.DeserializeObject<Order>(orderPayload);

                var payment = payments.FirstOrDefault(p => p.OrderId == order.Id);
                if (payment == null)
                {
                    return null;
                }

                order.Status = payment.IsSuccessful ? "Paid" : "Unpaid";

                order.InvoiceAdditions.ForEach(ia => ia.Id = Guid.NewGuid().ToString("N"));

                order.OrderItems.ForEach(a => a.OrderId = order.Id);

                order.OrderDate = order.CreatedAt;
                order.AmountPaid = payment.AmountPaid;

                payment.BranchId = order.BranchId;
                payment.DeliveryFee = order.DeliveryFee;
                payment.IsRecurring = order.IsRecurring;
                payment.RecurringInvoiceId = order.RecurringInvoiceId;
                payment.BranchName = order.BranchName;
                payment.EmployeeId = order.EmployeeId;
                payment.EmployeeName = order.EmployeeName;
                payment.Location = order.Location;
                payment.PaymentProcessor = order.PaymentRequest?.PaymentProcessorName;

                order.Payment = payment;

                return order;
            }
            catch (Exception e)
            {
                Logger.Error(e, e.Message);
            }

            return null;
        }


        private async Task PersistInvoiceAdditions(List<InvoiceAdditionEntity> invoiceAdditions)
        {
            var actor = Context.ActorOf(
                Props.Create(() => new DbPersistActor(_pgRepository, _cacheManager, _redisOptions, _aiPublisher,
                    _paymentReceivedDbOperation)),
                $"InvoiceAdditionsDbPersistor{Guid.NewGuid():N}");

            actor.Tell(new PersistInvoiceAdditionsToDb(invoiceAdditions));
            await Task.Delay(0);
        }

        private async Task PersistOrderItems(List<OrderItemsEntity> orderItems)
        {
            var actor = Context.ActorOf(
                Props.Create(() => new DbPersistActor(_pgRepository, _cacheManager, _redisOptions, _aiPublisher,
                    _paymentReceivedDbOperation)),
                $"OrderItemsDbPersistor{Guid.NewGuid():N}");

            actor.Tell(new PersistOrderItemsToDb(orderItems));
            await Task.Delay(0);
        }

        private async Task PersistPayments(List<PaymentEntity> payments)
        {
            var actor = Context.ActorOf(
                Props.Create(() => new DbPersistActor(_pgRepository, _cacheManager, _redisOptions, _aiPublisher,
                    _paymentReceivedDbOperation)),
                $"PaymentsDbPersistor{Guid.NewGuid():N}");

            actor.Tell(new PersistPaymentsToDb(payments));
            await Task.Delay(0);
        }

        private async Task PersistOrders(List<OrderEntity> orders)
        {
            var actor = Context.ActorOf(
                Props.Create(() => new DbPersistActor(_pgRepository, _cacheManager, _redisOptions, _aiPublisher,
                    _paymentReceivedDbOperation)),
                $"OrdersDbPersistor{Guid.NewGuid():N}");

            actor.Tell(new PersistOrdersToDb(orders));
            await Task.Delay(0);
        }
    }
}