using System.Collections.Generic;
using Hubtel.Orders.Consumers.Storage.Pg.Entities;

namespace Hubtel.Orders.Consumers.Plugins.Postgres.Messages
{
    public struct PersistInvoiceAdditionsToDb
    {
        public List<InvoiceAdditionEntity> InvoiceAdditionEntities { get; }

        public PersistInvoiceAdditionsToDb(List<InvoiceAdditionEntity> invocieAdditionEntities)
        {
            InvoiceAdditionEntities = invocieAdditionEntities;
        }
    }
}