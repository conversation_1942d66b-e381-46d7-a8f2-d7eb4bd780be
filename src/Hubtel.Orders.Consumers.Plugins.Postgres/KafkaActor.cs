using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Akka.Actor;
using Hubtel.Kafka.Host.Core;
using Hubtel.Orders.Consumers.Common;
using Hubtel.Orders.Consumers.Common.Actors;
using Hubtel.Orders.Consumers.Common.Models;
using JustEat.StatsD;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Hubtel.Orders.Consumers.Plugins.Postgres
{
    public class KafkaActor : BaseActor
    {
        private readonly IKafkaRawProducer _producerService;
        private readonly IStatsDPublisher _metrics;
        private readonly KafkaExtra _kafkaExtra;

       // private readonly IProducer<Null, string> _producer;

        public KafkaActor(IKafkaRawProducer producerService, IStatsDPublisher metrics
            , KafkaExtra kafkaExtra
            //IProducer<Null, string> producer
            )
        {
            _producerService = producerService;
            _metrics = metrics;
            _kafkaExtra = kafkaExtra;
            //_producer = producer;

            ReceiveAsync<ProduceToKafka>(async m=>await DoProduceToKafka(m));
        }

        private async Task DoProduceToKafka(ProduceToKafka message)
        {
            try
            {
                var ecommerceOrders = message.Orders.Where(o => o.IsEcommerceOrder).ToList();
                var programmableOrders = message.Orders.Where(o => o.IsProgrammableService && o.Payment.IsSuccessful).ToList();
                var paidOrders = message.Orders.Where(o => !o.IsProgrammableService && o.Payment.IsSuccessful).ToList();
                var paidAndUnpaid = message.Orders; //.Where(o => !o.Payment.IsSuccessful).ToList();
                var directSettlementOrders = message.Orders.Where(o=>!o.IsBulkSettlement && o.Payment.IsSuccessful).ToList();

                

                if (paidOrders.Any())
                {
                    _metrics.Increment(paidOrders.Count, Buckets.PaidOrders.Name);
                    Logger.Debug($"Pushing paid orders to kafka");

                    _metrics.Increment(message.Orders.Count, Buckets.OrderPaidPaymentPublishBucket.Name);
                    var topicMessageTuple = paidOrders.Select(p =>
                        Tuple.Create(_kafkaExtra.ProducePaidTopic, JsonConvert.SerializeObject(p))).ToList();
                    //await _producerService.Produce(topicMessageTuple);

                    foreach (var tuple in topicMessageTuple)
                    {
                        //await _producer.ProduceAsync(tuple.Item1, new Message<Null, string> {Value = tuple.Item2});
                        await _producerService.Produce(tuple.Item1, tuple.Item2);
                        Logger.Debug($"produced topic: {tuple.Item1} payload: {tuple.Item2} to kafka");
                    }

                    _producerService.Flush(TimeSpan.FromMilliseconds(10));

                }

                if (paidAndUnpaid.Any())
                {
                    _metrics.Increment(paidAndUnpaid.Count, Buckets.OrderPaidPaymentPublishBucket.Name);

                    var topicMessageTuple = paidAndUnpaid.Select(p =>
                        Tuple.Create(_kafkaExtra.ProducePaidAndUnpaidTopic, JsonConvert.SerializeObject(p))).ToList();

                    _metrics.Increment(paidAndUnpaid.Count, Buckets.OrderPaidAndUnpaidPaymentPublishBucket.Name);
                    //await _producerService.Produce(topicMessageTuple);

                    foreach (var tuple in topicMessageTuple)
                    {
                        //await _producer.ProduceAsync(tuple.Item1, new Message<Null, string> {Value = tuple.Item2});
                        await _producerService.Produce(tuple.Item1, tuple.Item2);
                        
                        Logger.Debug($"produced topic: {tuple.Item1} payload: {tuple.Item2} to kafka");

                    }

                    _producerService.Flush(TimeSpan.FromMilliseconds(10));

                }

                if (programmableOrders.Any())
                {

                    var topicMessageTuple = programmableOrders.Select(p =>
                        Tuple.Create(_kafkaExtra.ProducePaidProgrammableTopic, JsonConvert.SerializeObject(p))).ToList();

                    _metrics.Increment(programmableOrders.Count, Buckets.OrderPaidProgrammableService.Name);
                    //await _producerService.Produce(topicMessageTuple);

                    foreach (var tuple in topicMessageTuple)
                    {
                        //await _producer.ProduceAsync(tuple.Item1, new Message<Null, string> {Value = tuple.Item2});
                        await _producerService.Produce(tuple.Item1, tuple.Item2);
                        Logger.Debug($"produced topic: {tuple.Item1} payload: {tuple.Item2} to kafka");
                    }

                    _producerService.Flush(TimeSpan.FromMilliseconds(10));

                }

                if (ecommerceOrders.Any())
                {
                    var topicMessageTuple = ecommerceOrders.Select(p =>
                        Tuple.Create(_kafkaExtra.ProducePaidAndUnpaidEcommerceTopic, JsonConvert.SerializeObject(p))).ToList();

                    _metrics.Increment(ecommerceOrders.Count, Buckets.OrderPaidAndUnpaidEcommercePaymentPublishBucket.Name);
                    //await _producerService.Produce(topicMessageTuple);

                    foreach (var tuple in topicMessageTuple)
                    {
                        //await _producer.ProduceAsync(tuple.Item1, new Message<Null, string> {Value = tuple.Item2});
                        await _producerService.Produce(tuple.Item1, tuple.Item2);
                        Logger.Debug($"produced topic: {tuple.Item1} payload: {tuple.Item2} to kafka");
                    }

                    _producerService.Flush(TimeSpan.FromMilliseconds(10));
                }

                if (directSettlementOrders.Any())
                {
                    var topicMessageTuple = directSettlementOrders.Select(p =>
                        Tuple.Create(_kafkaExtra.DirectSettlementTopic, JsonConvert.SerializeObject(p))).ToList();
                    
                    foreach (var tuple in topicMessageTuple)
                    {
                        await _producerService.Produce(tuple.Item1, tuple.Item2);
                        Logger.Debug($"produced topic: {tuple.Item1} payload: {tuple.Item2} to kafka");
                    }

                    _producerService.Flush(TimeSpan.FromMilliseconds(10));
                }
            }
            catch (Exception e)
            {
                Logger.Error(e,e.Message);
            }

            Self.Tell(PoisonPill.Instance);
        }
    }

    public struct ProduceToKafka
    {
        public List<Order> Orders { get; }

        public ProduceToKafka(List<Order> orders)
        {
            Orders = orders;
        }
    }
}