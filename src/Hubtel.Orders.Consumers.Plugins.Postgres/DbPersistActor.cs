using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.Streams.Implementation.Fusing;
using Hubtel.Instrumentation;
using Hubtel.Instrumentation.Redis;
using Hubtel.Orders.Consumers.Common;
using Hubtel.Orders.Consumers.Common.Actors;
using Hubtel.Orders.Consumers.Plugins.Postgres.Messages;
using Hubtel.Orders.Consumers.Storage.Pg;
using Hubtel.Redis.Sdk;
using Hubtel.Redis.Sdk.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hubtel.Orders.Consumers.Plugins.Postgres
{
    public class DbPersistActor : BaseActor
    {
        private readonly IGenericRepository _repo;
        private readonly ICacheManager _cacheManager;
       // private readonly IRedisConnectionFactory _redisConnectionFactory;
        private readonly RedisConfiguration _redisConfig;
        private readonly IHubtelDiagnosticsPublisher _aiPublisher;
        private readonly IPaymentReceivedDbOperation _dbOperation;
        

        public DbPersistActor(IGenericRepository repo
            //,IRedisConnectionFactory redisConnectionFactory
            ,ICacheManager cacheManager
            ,IOptions<RedisConfiguration> redisConfig
            ,IHubtelDiagnosticsPublisher aiPublisher
            ,IPaymentReceivedDbOperation dbOperation
            )
        {
            _repo = repo;
            _cacheManager = cacheManager;
           // _redisConnectionFactory = redisConnectionFactory;
            _redisConfig = redisConfig.Value;
            _aiPublisher = aiPublisher;
            _dbOperation = dbOperation;
            
            ReceiveAsync<PersistOrdersToDb>(async m=>await DoPersistOrdersToDb(m));
            ReceiveAsync<PersistPaymentsToDb>(async m=>await DoPersistPaymentsToDb(m));
            ReceiveAsync<PersistOrderItemsToDb>(async m=>await DoPersistOrderItemsToDb(m));
            ReceiveAsync<PersistInvoiceAdditionsToDb>(async m=>await DoPersistInvoiceItemsToDb(m));
            ReceiveAsync<CleanupRedis>(async m=>await DoCleanupRedis(m));
        }

        private async Task DoCleanupRedis(CleanupRedis message)
        {
            try
            {
              //  var db = _redisConnectionFactory.Connection().GetDatabase();
                
                

                var paymentChannelGroups = message.Orders.GroupBy(o => o.PaymentRequest.PaymentProcessorRoute);

                var pendingTransactionsTaskList = new List<Task<long>>();
                var paymentTypeIgnoreList = new[] {"CASH", "COMMISSION"};

                foreach (var processorGroup in paymentChannelGroups)
                {

                    
                    var key = processorGroup.Key;
                    if (string.IsNullOrEmpty(key))
                    {
                        key = processorGroup.FirstOrDefault()?.PaymentRequest.Channel;
                    }
                    var setKeyName = $"{key}:pending";
                    var setValues = processorGroup
                        .Where(pt =>
                            !paymentTypeIgnoreList.Contains(pt.PaymentRequest.PaymentType,
                                StringComparer.OrdinalIgnoreCase))
                        //.Select(c => (RedisValue) ($"{c.PaymentRequest.HubtelReference}:{c.Id}")).ToList();
                        .Select(c => ($"{c.PaymentRequest.HubtelReference}:{c.Id}")).ToList();


                    //pendingTransactionsTaskList.Add(db.SetRemoveAsync(setKeyName, setValues.ToArray()));
                    pendingTransactionsTaskList.Add(_cacheManager.RemoveSetMembers(setKeyName,-1, setValues.ToArray()));

                }

                var redisPopTaskList = new List<Task<bool>>();

                //message.Orders.ForEach(p => redisPopTaskList.Add(db.KeyDeleteAsync(p.Id)));
                message.Orders.ForEach(p => redisPopTaskList.Add(_cacheManager.Delete(p.Id)));


                using (var rd = _aiPublisher.StartRedisDiagnostics(new RedisDiagnosticConfig
                    {Host = _redisConfig.Host, DbNumber = _redisConfig.Database.ToString(), Port = _redisConfig.Port}))
                {
                    try
                    {
                        await Task.WhenAll(redisPopTaskList);

                        rd.Command = $"DEL {redisPopTaskList.Count} orders";
                    }
                    catch (Exception e)
                    {
                        rd.Exception = e;
                        rd.Failed = true;
                    }
                }

                using (var rd = _aiPublisher.StartRedisDiagnostics(new RedisDiagnosticConfig
                    {Host = _redisConfig.Host, DbNumber = _redisConfig.Database.ToString(), Port = _redisConfig.Port}))
                {
                    try
                    {
                        await Task.WhenAll(pendingTransactionsTaskList);

                        rd.Command = $"SREM {pendingTransactionsTaskList.Count} pending transactions";
                    }
                    catch (Exception e)
                    {
                        rd.Exception = e;
                        rd.Failed = true;
                    }
                }

            }
            catch (Exception e)
            {

            }
            finally
            {
                
            }
            Self.Tell(PoisonPill.Instance);
        }

        private async Task DoPersistPaymentsToDb(PersistPaymentsToDb message)
        {
            if (await _dbOperation.PersistPayments(message.PaymentEntities))
            {
                Logger.Info($"successfully inserted {message.PaymentEntities.Count} payments");
            }
            else
            {
                Logger.Warning($"could not insert {message.PaymentEntities.Count} payments");
            }
            Self.Tell(PoisonPill.Instance);
        }

        private async Task DoPersistInvoiceItemsToDb(PersistInvoiceAdditionsToDb message)
        {
            if (!message.InvoiceAdditionEntities.Any())
            {
                Logger.Debug($"No invoice additions, ignoring...");
                Self.Tell(PoisonPill.Instance);
                return;
            }

            if (await _dbOperation.PersistInvoiceAdditions(message.InvoiceAdditionEntities))
            {
                Logger.Info($"successfully inserted {message.InvoiceAdditionEntities.Count} invoice additions");
            }
            else
            {
                Logger.Warning($"could not insert {message.InvoiceAdditionEntities.Count} invoice additions");
            }

            Self.Tell(PoisonPill.Instance);
        }

        private async Task DoPersistOrderItemsToDb(PersistOrderItemsToDb message)
        {
            if (await _dbOperation.PersistOrderItems(message.OrderItems))
            {
                Logger.Info($"successfully inserted {message.OrderItems.Count} order items");
            }
            else
            {
                Logger.Warning($"could not insert {message.OrderItems.Count} order items");
            }

            Self.Tell(PoisonPill.Instance);
        }

        private async Task DoPersistOrdersToDb(PersistOrdersToDb message)
        {
            if (await _dbOperation.PersistOrders(message.Orders))
            {
                Logger.Info($"successfully inserted {message.Orders.Count} orders");
            }
            else
            {
                Logger.Warning($"could not insert {message.Orders.Count} orders");
            }
            Self.Tell(PoisonPill.Instance);
        }
    }
}