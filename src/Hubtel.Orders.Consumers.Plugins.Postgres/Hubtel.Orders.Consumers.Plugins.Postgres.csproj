<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath>../Hubtel.Orders.Consumers.OrderPaid/bin/Debug/</OutputPath>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Hubtel.Orders.Consumers.Common.Actors\Hubtel.Orders.Consumers.Common.Actors.csproj" />
    <ProjectReference Include="..\Hubtel.Orders.Consumers.Storage.Pg\Hubtel.Orders.Consumers.Storage.Pg.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="10.0.0" />
    <PackageReference Include="Confluent.Kafka" Version="1.5.2" />
    <PackageReference Include="Flurl.Http" Version="2.4.2" />
    <PackageReference Include="Hubtel.Redis.Sdk" Version="1.0.9" />
    <PackageReference Include="Pluralize.NET" Version="1.0.2" />
    <PackageReference Include="Dapper" Version="2.0.35" />
    <PackageReference Include="DapperExtensions.DotnetCore" Version="1.0.1" />

    <PackageReference Include="JustEat.StatsD" Version="4.1.0" />
    
    <PackageReference Include="Akka.Streams" Version="1.4.10" />

    <PackageReference Include="System.Data.SqlClient" Version="4.8.2" />
    <PackageReference Include="Npgsql" Version="4.1.5" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="3.1.4" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="3.1.6" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="3.1.6" />
    <PackageReference Include="Hubtel.Instrumentation" Version="1.0.15" />
    <PackageReference Include="Hubtel.Kafka.Host" Version="1.2.7" />
  </ItemGroup>

</Project>
