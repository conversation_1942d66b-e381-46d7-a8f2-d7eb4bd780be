{"ApplicationInsights": {"InstrumentationKey": "to be changed to production"}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "GELF": {"Host": "graylog.internal.hubtel.com", "Port": 12202, "LogSource": "Hubtel.Orders.Consumers.SavedOrders", "Facility": "Hubtel.Orders.Consumers.SavedOrders", "Environment": "Production", "LogLevel": {"Default": "Debug"}}}, "KafkaConsumerConfig": {"BootstrapServers": "localhost:9092", "GroupId": "Hubtel.SavedOrders.Workers", "Topics": ["hubtel.sales.order_paid"], "ExtraProperties": {"auto.offset.reset": "latest"}}, "KafkaProducerConfig": {"BootstrapServers": "localhost:9092"}, "StatsD": {"Host": "*************", "Port": "8125", "Prefix": "savedorderconsumer"}, "SavedOrderConfig": {"SavedOrderUrl": "http://localhost:9052/api/v1/SavedOrders", "Auth": "Hu<PERSON>el-Bearer ODBlNTJkN2ExY2RmNDZkOGFlNzJhNGE3NTdjNGFhOGU6eyJBY2NvdW50SWQiOiJwcml2YXRlYWNjZXNzIn0="}}