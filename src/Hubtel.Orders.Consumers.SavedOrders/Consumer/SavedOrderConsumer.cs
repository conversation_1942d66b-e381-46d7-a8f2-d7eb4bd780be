using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using Hubtel.Kafka.Host.Core;
using Hubtel.Orders.Consumers.SavedOrders.Models;
using Hubtel.Orders.Consumers.SavedOrders.Services;
using JustEat.StatsD;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hubtel.Orders.Consumers.SavedOrders.Consumer
{
    public class SavedOrderConsumer:KafkaConsumerBase
    {
        private readonly IStatsDPublisher _metrics;
        private readonly ILogger<SavedOrderConsumer> _logger;
        private readonly ISavedOrderService _savedOrderService;

        public SavedOrderConsumer(ILogger<SavedOrderConsumer> logger, IStatsDPublisher metrics, ISavedOrderService savedOrderService)
        {
            _logger = logger;
            _metrics = metrics;
            _savedOrderService = savedOrderService;
        }

        [ConsumeTopic(FromType = typeof(IOptions<KafkaConsumerConfig>),PropertyName = nameof(KafkaConsumerConfig.TopicsAsSingleString))]
        public async Task HandleBulkMessage(OrderPaidEventMessage orders)
        {
            if (orders.IsMultiCart || orders.IsMultiCartitem)
            {
                _metrics.Gauge(Process.GetCurrentProcess().WorkingSet64 / (1024.0 * 1024.0), "");
                await _savedOrderService.DeleteSavedOrder(orders.Payment.OrderId);   
            }
        }
    }
}