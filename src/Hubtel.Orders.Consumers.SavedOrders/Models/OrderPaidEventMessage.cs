using System;
using System.Collections.Generic;

namespace Hubtel.Orders.Consumers.SavedOrders.Models
{
    public class OrderPaidEventMessage
    {
        public string Id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string IntegrationChannel { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PosDeviceId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PosDeviceType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime OrderDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string OrderNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Note { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Status { get; set; } //PAID, PARITALLY PAID, UNPAID, CANCELLED
        /// <summary>
        /// 
        /// </summary>
        public string AssignedTo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string EmployeeId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CustomerMobileNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CustomerName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BranchId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BranchName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public float? TaxRate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? TaxAmount { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public float? DiscountRate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? DiscountAmount { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal Subtotal { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal TotalAmountDue { get; set; }

        /// <summary>
        /// This is a total of all payments
        /// </summary>
        public decimal AmountPaid { get; set; }

        /// <summary>
        /// This is a total of all refunds
        /// </summary>
        public decimal AmountRefunded { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PaymentTypes { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Location { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Currency { get; set; }
        /// <summary>
        /// Payment object for json
        /// </summary>
        public OrderPaidEventPayment Payment { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsTransaction { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? IsFulfilled { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int ConsumerRating { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ConsumerFeedback { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CustomerEmail { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BusinessEmail { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BusinessMobileNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BusinessName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string FcmCustomer { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string FcmDevice { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal AmountDueProducer { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double CustomerReward { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SenderId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string LogoUrl { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ReturnUrl { get; set; }
        /// <summary>
        /// 
        /// </summary>
        /// [StringLength(40)]
        public string BranchEmail { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BranchPhoneNumber { get; set; }

        public ICollection<OrderItemDto> OrderItems { get; set; }
        
        public ICollection<InvoiceAdditionDto> InvoiceAdditions { get; set; }

        public bool HasDelivery { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CancellationUrl { get; set; }

        /// <summary>
        /// Human readable location of a delivery, e.g. Kokomlemle
        /// </summary>
        public string DeliveryLocationName { get; set; }

        /// <summary>
        /// Indicates whether the invoice was created due to a recurring payment
        /// </summary>
        public bool IsRecurring { get; set; }
        /// <summary>
        /// RecurringInvoiceID
        /// </summary>
        public string RecurringInvoiceId { get; set; }
        /// <summary>
        /// Determines whether order is for a service so that certain services will be rendered
        /// </summary>
        public bool IsProgrammableService { get; set; }

        public string BusinessId { get; set; }
        
        public bool IsMultiCart { get; set; }
        public bool IsMultiCartitem { get; set; }
        public List<MultiCartItem> MultiCartIds { get; set; }
    }
    
    public class MultiCartItem
    {
        public string Value { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class OrderPaidEventPayment
    {
        /// <summary>
        /// 
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BusinessId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PaymentType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string OrderId { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string MomoPhoneNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MomoChannel { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string MomoToken { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string TransactionId { get; set; } // from merchant account
        /// <summary>
        /// 
        /// </summary>
        public string ExternalTransactionId { get; set; }// from telco to merchant account
        /// <summary>
        /// 
        /// </summary>
        public decimal AmountAfterCharges { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal Charges { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool ChargeCustomer { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal AmountPaid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime PaymentDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Note { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PosDeviceId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PosDeviceType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string EmployeeId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CustomerMobileNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CustomerName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BranchId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BranchName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsRefund { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsSuccessful { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ReceiptNumber { get; set; } //this is autogenerated increment for organization for successful payments
        /// <summary>
        /// 
        /// </summary>
        public string Location { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Currency { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Scheme { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Card { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Tid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Authorization { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Mid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CardTransactionId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal AmountTendered { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal Balance { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal DeliveryFee { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool HasDelivery { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string HubtelAccountId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int FineractSavingsAccountId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string FineractPaymentTypeId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CallbackUrl { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ClientReference { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ProviderDescription { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string StatusCode { get; set; }
        }
    }
    public class OrderItemDto
    {
        public string ItemId { get; set; }

        public string OrderId { get; set; }

        public string Name { get; set; }

        public int Quantity { get; set; }

        public decimal UnitPrice { get; set; }

        public string ServiceRequestId { get; set; }

    }
    
    public class InvoiceAdditionDto
    {
        public string Id { get; set; }
        public string BranchId { get; set; }
        public bool? IsInclusive { get; set; }
        public short? Sequence { get; set; }
        public string Name { get; set; }
        public bool? IsFlatFee { get; set; }
        public decimal? Figure { get; set; }
        public string CalculationMethod { get; set; }
        public decimal ComputedValue { get; set; }
        public bool? IsActive { get; set; }
    }
