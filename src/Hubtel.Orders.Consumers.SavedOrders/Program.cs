using System;
using System.Collections.Generic;
using Gelf.Extensions.Logging;
using Hubtel.Instrumentation.Extensions;
using Hubtel.Kafka.Host;
using Hubtel.Kafka.Host.Core;
using Hubtel.Kafka.Host.Core.ConsumerLogic;
using Hubtel.Kafka.Host.Extensions;
using Hubtel.Orders.Consumers.SavedOrders.Services;
using JustEat.StatsD;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Hubtel.Orders.Consumers.SavedOrders
{
    class Program
    {
        public static void Main(string[] args)
        {
            var host = CreateKafkaBuilder(args).Build();

            

            host.Run();
        }

        public static IHostBuilder CreateKafkaBuilder(string[] args) =>
          KafkaHost.CreateDefaultBuilder(args)
              .ConfigureServices((hostContext, services) =>
              {
                    //mandatory
                    services.Configure<KafkaConsumerConfig>(options =>
                      hostContext.Configuration.GetSection(nameof(KafkaConsumerConfig)).Bind(options));
                  services.Configure<KafkaProducerConfig>(options =>
                      hostContext.Configuration.GetSection(nameof(KafkaProducerConfig)).Bind(options));
                  services.Configure<SavedOrderConfig>(hostContext.Configuration.GetSection(nameof(SavedOrderConfig)));


                  services.AddKafkaProducerAgent(hostContext.Configuration["KafkaProducerConfig:BootstrapServers"]);
                  
                  // bind the config to host options
                  services.Configure<HostOptions>(c =>
                      hostContext.Configuration.GetSection(nameof(HostOptions)).Bind(c));

                  services.AddApplicationInsightsTelemetryWorkerService(
                      hostContext.Configuration["ApplicationInsights:InstrumentationKey"]);
                  services.AddApplicationInsightsTelemtryHubtel(
                      hostContext.Configuration["ApplicationInsights:InstrumentationKey"]);

                  services.AddScoped<ISavedOrderService, SavedOrderService>();
                  
                  services.AddStatsD((provider) =>
                  {
                      int.TryParse(hostContext.Configuration["StatsD:Port"], out int port);
                      return new StatsDConfiguration()
                      {
                          Host = hostContext.Configuration["StatsD:Host"],
                          Port = port,
                          Prefix = hostContext.Configuration["StatsD:Prefix"],
                          OnError = ex => true
                      };
                  });
                  services.AddLogging(loggingBuilder =>
                      loggingBuilder.AddConfiguration(hostContext.Configuration.GetSection("Logging"))
                          .ClearProviders().SetMinimumLevel(LogLevel.Debug).AddConsole()

                          .AddGelf((c) =>
                          {

                              c.AdditionalFields = new Dictionary<string, object>()
                              {
                                    {"facility", hostContext.Configuration.GetSection("Logging")["GELF:Facility"]},
                                    {
                                        "Environment",
                                        hostContext.Configuration.GetSection("Logging")["GELF:Environment"]
                                    },
                                    {"machine_name", Environment.MachineName}
                              };
                              c.Host = hostContext.Configuration.GetSection("Logging")["GELF:Host"];
                              c.LogSource = hostContext.Configuration.GetSection("Logging")["GELF:LogSource"];
                              c.Port = int.Parse(hostContext.Configuration.GetSection("Logging")["GELF:Port"]);

                          }));

              });
    }
}