<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>netcoreapp3.1</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="8.0.1" />
        <PackageReference Include="Hubtel.Kafka.Host" Version="1.2.7" />
        <PackageReference Include="Confluent.Kafka" Version="1.5.2" />
        <PackageReference Include="Confluent.Kafka.Extensions" Version="1.0.0" />
        <PackageReference Include="Gelf.Extensions.Logging" Version="2.0.0" />
        <PackageReference Include="Hubtel.Instrumentation" Version="1.0.15" />
        <PackageReference Include="Hubtel.Redis.Sdk" Version="1.0.9" />
        <PackageReference Include="JustEat.StatsD" Version="4.1.0" />

        <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.14.0" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="3.1.6" />

        <PackageReference Include="Akka.DI.AutoFac" Version="1.4.1" />
        <PackageReference Include="Akka.Logger.Serilog" Version="1.4.8" />
        <PackageReference Include="Akka.Serialization.Hyperion" Version="1.4.10" />
        <PackageReference Include="Akka.DI.Core" Version="1.4.10" />
        <PackageReference Include="Akka.Streams" Version="1.4.10" />
        <PackageReference Include="Autofac" Version="6.0.0" />
        <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="7.0.2" />


        <PackageReference Include="Newtonsoft.Json" Version="12.0.3" />
        <PackageReference Include="Npgsql" Version="4.1.5" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="3.1.4" />
        <PackageReference Include="Flurl.Http" Version="2.4.2" />
        <PackageReference Include="StackExchange.Redis" Version="2.1.58" />
    </ItemGroup>

    <ItemGroup>
      <None Update="appsettings.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
