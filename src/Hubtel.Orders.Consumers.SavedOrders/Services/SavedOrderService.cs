using System;
using System.Net.Http;
using System.Threading.Tasks;
using Flurl.Http;
using JustEat.StatsD;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hubtel.Orders.Consumers.SavedOrders.Services
{
    public class SavedOrderService:ISavedOrderService
    {
        private readonly ILogger<SavedOrderService> _logger;
        private readonly IStatsDPublisher _metrics;
        private readonly IOptions<SavedOrderConfig> _savedOrderOptions;

        public SavedOrderService(ILogger<SavedOrderService> logger, IStatsDPublisher metrics, IOptions<SavedOrderConfig> savedOrderOptions)
        {
            _logger = logger;
            _metrics = metrics;
            _savedOrderOptions = savedOrderOptions;
        }

        public async Task DeleteSavedOrder(string orderId)
        {
            HttpResponseMessage servResp;

            try
            {
                servResp = await $"{_savedOrderOptions.Value.SavedOrderUrl}/{orderId}".WithHeader("Authorization", _savedOrderOptions.Value.Auth)
                    .AllowAnyHttpStatus().DeleteAsync();
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occured");
                throw;
            }
            
            var raw = await servResp.Content.ReadAsStringAsync();

            if (servResp.IsSuccessStatusCode)
            {
                _logger.LogInformation("record with {order_Id} deleted successfully from saved orders", orderId);
                return;
            }
            _logger.LogInformation("record with {order_Id} not found", orderId);
        }
    }
}