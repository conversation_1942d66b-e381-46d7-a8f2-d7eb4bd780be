import json

from kafka import KafkaProducer

producer = KafkaProducer(bootstrap_servers=['127.0.0.1:9092'], api_version=(0, 10), value_serializer=lambda m: json.dumps(m).encode('ascii'))

# produce asynchronously

i = 0

while i < 1:

  producer.send('hubtel.payments.payment_received',value = {
      "BusinessId": "ae549a0bbd9e453fa3aa19a206b6cbc3",
      "CreatedAt": "2024-05-30T08:13:20.4944604Z",
      "PaymentType": "mobilemoney",
      "OrderId": "5975a14ef43d46208322d5cb1ab8de0d",
      "HubtelAccountId": "ae549a0bbd9e453fa3aa19a206b6cbc3",
      "FineractSavingsAccountId": 11673,
      "TransactionId": "18FC88F841D70349070",
      "ExternalTransactionId": "***********",
      "CustomerMobileNumber": "************",
      "CustomerName": "************",
      "AmountAfterCharges": 0.98,
      "Fee": 0.02,
      "DeliveryFee": 0.0,
      "ChargeCustomer": False,
      "AmountPaid": 1.0,
      "PaymentDate": "2024-05-30T08:13:20.4944604Z",
      "Description": "SportyBet",
      "IsSuccessful": True,
      "Currency": "GH",
      "Country": "Gh",
      "Card": None,
      "MobileMoney": {
          "PhoneNumber": "************",
          "Network": "mtn-gh",
          "Token": None
      },
      "Cash": None,
      "CallbackUrl": "https://www.sportybet.com/api/gh/pocket/c1/paych/hubtel/callback",
      "ClientReference": "240530081319pch24089175",
      "ProviderDescription": "The MTN Mobile Money payment has been approved and processed successfully.",
      "StatusCode": "0000",
      "ReceiptNumber": "********",
      "AmountTendered": 0.0,
      "IsRefund": False,
      "IsRecurring": False,
      "RecurringInvoiceId": None,
      "RefundTransactionId": None,
      "RefundRequestedDate": None,
      "RefundCompletedDate": None,
      "RefundRequestedBy": None,
      "RefundDestinationType": None,
      "RefundDestination": None,
      "AmountRefunded": 0.0,
      "RefundStatus": None,
      "ProviderResponseCode": "SUCCESSFUL"
  })

  i = i + 1

  print("produced")

producer.flush()