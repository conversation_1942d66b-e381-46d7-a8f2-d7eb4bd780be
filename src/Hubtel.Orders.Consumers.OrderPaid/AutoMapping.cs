using AutoMapper;
using Hubtel.Orders.Consumers.Common.Models;
using Hubtel.Orders.Consumers.Storage.Pg.Entities;

namespace Hubtel.Orders.Consumers.PaymentReceived
{
    public class AutoMapping : Profile
    {
        public AutoMapping()
        {
            CreateMap<Order, OrderEntity>(); 
            CreateMap<OrderItem, OrderItemsEntity>();
            CreateMap<InvoiceAddition, InvoiceAdditionEntity>(); 
            CreateMap<Payment, PaymentEntity>();
        }
    }
}