{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "GELF": {"Host": "graylog.internal.hubtel.com", "Port": 12202, "LogSource": "Hubtel.Orders.Consumers.PaymentReceived", "Facility": "Hubtel.Orders.Consumers.PaymentReceived", "Environment": "Production", "LogLevel": {"Default": "Debug"}}}, "RedisConfiguration": {"Host": "127.0.0.1", "Port": "6379", "Database": 5, "Timespan": 7}, "Statsd": {"server": "localhost"}, "ConnectionStrings": {"WriterConnection": "Server=localhost;Port=5432;Database=HubtelUnifiedSales;UserId=********;Password=''", "ReaderConnection": "Server=localhost;Port=5432;Database=HubtelUnifiedSales;UserId=********;Password=''"}, "ApplicationInsights": {"InstrumentationKey": "109f6f9f-e339-475c-8f3a-bf19c328118d"}, "KafkaConsumerConfig": {"BootstrapServers": "localhost:9092", "GroupId": "Hubtel.PaymentReceivedCorrector.Workers", "Topics": ["hubtel.payments.payment_received", "hubtel.payment.payment_received_cash"], "ExtraProperties": {"auto.offset.reset": "latest"}}, "KafkaProducerConfig": {"BootstrapServers": "localhost:9092"}, "KafkaExtra": {"BatchNumberMessages": 5000, "BatchTimeout": 50, "ProducePaidTopic": "hubtel.sales.order_paid", "ProducePaidAndUnpaidTopic": "hubtel.sales.order_payment_state_received", "ProducePaidProgrammableTopic": "hubtel.sales.programmable_order_paid", "ProducePaidAndUnpaidEcommerceTopic": "hubtel.sales.ecommerce_payment_state_received", "ErrorTopic": "hubtel.sales.order_corrector_error", "DirectSettlementTopic": "hubtel.sales.direct_settlement"}, "HostOptions": {"ShutdownTimeout": "00:00:15"}, "StatsD": {"Host": "**********", "Port": 8125, "Prefix": "order_scheduler"}, "PgConfig": {"ConnectionString": "Server=localhost;Database=HubtelUnifiedSales;UserId=********;Password=********", "WriterConnection": "Server=localhost;Database=HubtelUnifiedSales;UserId=********;Password=********", "ReaderConnection": "Server=localhost;Database=HubtelUnifiedSales;UserId=********;Password=********"}, "Plugins": [{"Name": "Hubtel.Orders.Consumers.Plugins.Postgres.dll", "ActorName": "PaymentReceivedActor", "RouterInstances": 4, "Type": "Hubtel.Orders.Consumers.Plugins.Postgres.PaymentReceivedActor"}], "EsConfig": {"Url": "http://localhost:9200/neworders/_doc/"}, "MultiCartPaymentConfig": {"MultiCartPaymentType": "hubtel_multicart"}, "CardVerficationConfig": {"BusinessId": "NHcc0b10e9f2c84026806bb5f30031084b"}}