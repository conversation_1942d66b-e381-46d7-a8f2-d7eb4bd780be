ALTER TABLE "Payments"
ADD CONSTRAINT payment_uniq_ordertran UNIQUE ("OrderId", "ExternalTransactionId") WHERE ExternalTransactionId IS NOT NULL;
;;
CREATE OR REPLACE FUNCTION create_payment_partition_and_insert() <PERSON><PERSON><PERSON><PERSON> trigger AS
	$BODY$
	DECLARE
      partition_date TEXT;
      partition TEXT;
    BEGIN
      partition_date := to_char(NEW."CreatedAt",'YYYY_MM_DD');
      partition := TG_TABLE_NAME || '_' || partition_date;
      IF NOT EXISTS(SELECT relname FROM pg_class WHERE relname=partition) THEN
        RAISE NOTICE 'A daily partition is being created %',partition;
        EXECUTE 'CREATE TABLE "' || partition || '" (check (("CreatedAt" >= ''' ||DATE_TRUNC('day',NEW."CreatedAt")|| ''' ) and ("CreatedAt" <= '''|| NEW."CreatedAt"::date + 1 - interval '1 sec'||'''))) INHERITS ("' || TG_TABLE_NAME || '");';
		EXECUTE 'CREATE UNIQUE INDEX ' || partition || '_uniq ON "' || partition || '" ("OrderId", "ExternalTransactionId") WHERE Length("ExternalTransactionId") > 0;';
      END IF;
      EXECUTE 'INSERT INTO "' || partition || '" SELECT("' || TG_TABLE_NAME || '" ' || quote_literal(NEW) || ').* ON CONFLICT ("OrderId", "ExternalTransactionId") WHERE Length("ExternalTransactionId") > 0 DO NOTHING;';
      RETURN NULL;
    END;
	$BODY$
LANGUAGE plpgsql VOLATILE
COST 100;
;;
CREATE TRIGGER Paymentstrigger
BEFORE INSERT ON "Payments"
FOR EACH ROW EXECUTE PROCEDURE create_payment_partition_and_insert();