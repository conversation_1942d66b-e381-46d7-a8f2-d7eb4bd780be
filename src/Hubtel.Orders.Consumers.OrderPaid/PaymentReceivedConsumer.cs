using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Akka.Actor;
using Hubtel.Kafka.Host.Core;
using Hubtel.Orders.Consumers.Common;
using Hubtel.Orders.Consumers.Common.Actors;
using Hubtel.Orders.Consumers.Common.Actors.Messages;
using Hubtel.Orders.Consumers.Common.Models;
using JustEat.StatsD;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hubtel.Orders.Consumers.PaymentReceived
{
    public class PaymentReceivedConsumer:KafkaConsumerBase
    {
        private readonly ILogger<PaymentReceivedConsumer> _logger;
        private readonly IStatsDPublisher _metrics;
        private readonly KafkaExtra _kafakExtra;

        public PaymentReceivedConsumer(ILogger<PaymentReceivedConsumer> logger
            ,IOptions<KafkaExtra> kafakExtra
            ,IStatsDPublisher metrics)
        {
            _logger = logger;
            _metrics = metrics;
            _kafakExtra = kafakExtra.Value;
        }
        
        [ConsumeTopic(FromType = typeof(IOptions<KafkaConsumerConfig>),PropertyName = nameof(KafkaConsumerConfig.TopicsAsSingleString))]
        public async Task HandleBulkMessage(List<PaymentReceivedMessage> result)
        {
            _metrics.Gauge(Process.GetCurrentProcess().WorkingSet64 / (1024.0 * 1024.0), Buckets.MemoryConsumptionBucket.Name);
            
            var list = new List<Payment>();

            _logger.LogDebug($"received {list.Count} from Kafka");

            foreach (var paymentReceivedMessage in result)
            {
                list.Add(paymentReceivedMessage.ToPayment());
            }
            TopLevelActors.MainActor.Tell(new HandleBatchPayments(list));
            
            await Task.Delay(0);
        }

        
       

        public override Task ConsumingStopped()
        {
            _logger.LogInformation($"consumer stopped at {DateTime.UtcNow}");
            return Task.CompletedTask;
        }
    }
}