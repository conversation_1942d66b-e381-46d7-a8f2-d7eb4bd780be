using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;
using Akka.Actor;
using Akka.Configuration;
using Akka.DI.AutoFac;
using Akka.DI.Core;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using AutoMapper;
using Gelf.Extensions.Logging;
using Hubtel.Instrumentation.Extensions;
using Hubtel.Kafka.Host;
using Hubtel.Kafka.Host.Core;
using Hubtel.Kafka.Host.Core.ConsumerLogic;
using Hubtel.Kafka.Host.Extensions;
using Hubtel.Orders.Consumers.Common;
using Hubtel.Orders.Consumers.Common.Actors;
using Hubtel.Orders.Consumers.Common.Models;
using Hubtel.Orders.Consumers.Storage.Pg;
using Hubtel.Redis.Sdk.Extensions;
using Hubtel.Redis.Sdk.Options;
using JustEat.StatsD;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;


namespace Hubtel.Orders.Consumers.PaymentReceived
{
    class Program
    {
        

        public static void Main(string[] args)
        {
            var host = CreateKafkaBuilder(args).Build();

            

            host.Run();
        }

        public static IHostBuilder CreateKafkaBuilder(string[] args) =>
          KafkaHost.CreateDefaultBuilder(args)
              .ConfigureServices((hostContext, services) =>
              {
                    //mandatory
                    services.Configure<KafkaConsumerConfig>(options =>
                      hostContext.Configuration.GetSection(nameof(KafkaConsumerConfig)).Bind(options));
                  services.Configure<KafkaProducerConfig>(options =>
                      hostContext.Configuration.GetSection(nameof(KafkaProducerConfig)).Bind(options));
                  services.Configure<MultiCartPaymentConfig>(options =>
                      hostContext.Configuration.GetSection(nameof(MultiCartPaymentConfig)).Bind(options));
                  services.Configure<CardVerficationConfig>(options =>
                      hostContext.Configuration.GetSection(nameof(CardVerficationConfig)).Bind(options));


                  services.AddSingleton<IKafkaConsumerLogicBase, MessageGroupConsumerLogic<PaymentReceivedMessage>>();
                  services.AddKafkaProducerAgent(hostContext.Configuration["KafkaProducerConfig:BootstrapServers"]);

                  services.Configure<DatabaseConnectionString>(
                      hostContext.Configuration.GetSection("ConnectionStrings"));

                  // bind the config to host options
                  services.Configure<HostOptions>(c =>
                      hostContext.Configuration.GetSection(nameof(HostOptions)).Bind(c));

                  services.Configure<KafkaExtra>(options =>
                      hostContext.Configuration.GetSection(nameof(KafkaExtra)).Bind(options));

                  services.Configure<List<OrderSchedulerPlugin>>(options =>
                      hostContext.Configuration.GetSection("Plugins").Bind(options));

                  services.AddApplicationInsightsTelemetryWorkerService(
                      hostContext.Configuration["ApplicationInsights:InstrumentationKey"]);
                  services.AddApplicationInsightsTelemtryHubtel(
                      hostContext.Configuration["ApplicationInsights:InstrumentationKey"]);

                  services.AddSingleton<IGenericRepository, PgGenericRepository>();
                  services.AddSingleton<IPaymentReceivedDbOperation, PaymentReceivedDbOperation>();

                  services.Configure<PgConfig>(options =>
                      hostContext.Configuration.GetSection(nameof(PgConfig)).Bind(options));

                  services.AddStatsD((provider) =>
                  {
                      int.TryParse(hostContext.Configuration["StatsD:Port"], out int port);
                      return new StatsDConfiguration()
                      {
                          Host = hostContext.Configuration["StatsD:Host"],
                          Port = port,
                          Prefix = hostContext.Configuration["StatsD:Prefix"],
                          OnError = ex => true
                      };
                  });
                  services.AddLogging(loggingBuilder =>
                      loggingBuilder.AddConfiguration(hostContext.Configuration.GetSection("Logging"))
                          .ClearProviders().SetMinimumLevel(LogLevel.Debug).AddConsole()

                          .AddGelf((c) =>
                          {

                              c.AdditionalFields = new Dictionary<string, object>()
                              {
                                    {"facility", hostContext.Configuration.GetSection("Logging")["GELF:Facility"]},
                                    {
                                        "Environment",
                                        hostContext.Configuration.GetSection("Logging")["GELF:Environment"]
                                    },
                                    {"machine_name", Environment.MachineName}
                              };
                              c.Host = hostContext.Configuration.GetSection("Logging")["GELF:Host"];
                              c.LogSource = hostContext.Configuration.GetSection("Logging")["GELF:LogSource"];
                              c.Port = int.Parse(hostContext.Configuration.GetSection("Logging")["GELF:Port"]);

                          }));


                  services.AddAutoMapper(typeof(Program));
                  services.AddSingleton(typeof(ActorSystem), serviceProvider => TopLevelActors.OrderSchedulerActorSystem);

                  services.Configure<RedisConfiguration>(options => hostContext.Configuration.GetSection(nameof(RedisConfiguration)).Bind(options));
                  

                  var hubtelRedisSdkSection = hostContext.Configuration.GetSection(nameof(RedisConfiguration));
                  services.AddHubtelRedisSdk(new RedisConfiguration
                  {
                      Database = hubtelRedisSdkSection.GetValue<int>("Database"),
                      Host = hubtelRedisSdkSection.GetValue<string>("Host"),
                      Port = hubtelRedisSdkSection.GetValue<string>("Port")
                  });
                  services.Configure<EsConfig>(options => hostContext.Configuration.GetSection(nameof(EsConfig)).Bind(options));

                  //Actors Configuration
                  var builder = new ContainerBuilder();

                  builder.Populate(services);

                  builder.RegisterType<MainActor>();



                  var plugins = hostContext.Configuration.GetSection("Plugins")
                      .GetChildren()
                      .ToList()
                      .Select(x => new OrderSchedulerPlugin
                      {
                          ActorName = x.GetValue<string>("ActorName"),
                          Type = x.GetValue<string>("Type"),
                          Name = x.GetValue<string>("Name"),
                          RouterInstances = x.GetValue<int>("RouterInstances")
                      });

                  var p = Assembly.GetExecutingAssembly();
                  string path = Path.GetDirectoryName(p.Location);

                  var directory = path;
                  var handlersList = new List<Type>();

                  foreach (var plugin in plugins)
                  {
                      try
                      {
                          var asm = AssemblyLoadContext.Default.LoadFromAssemblyPath(Path.Combine(directory, $"{plugin.Name}"));
                          var actorType = asm.GetType(plugin.Type);
                          handlersList.Add(actorType);
                          builder.RegisterType(actorType);

                      }
                      catch (Exception e)
                      {

                          throw;
                      }

                  }

                  builder.RegisterInstance(new PluginList(handlersList)).As<IPluginList>();
                  var container = builder.Build();


                  TopLevelActors.ServiceProvider = container;


                  var fallbackConfig = ConfigurationFactory.ParseString(@"
                    akka.suppress-json-serializer-warning=true
                    akka.loglevel = DEBUG
                    akka.loggers = [""Hubtel.Orders.Consumers.Common.Actors.SimpleLoggerActor, Hubtel.Orders.Consumers.Common.Actors""]
                ");
                  TopLevelActors.OrderSchedulerActorSystem = ActorSystem.Create(TopLevelActors.ActorSystemName, fallbackConfig);
                  

                  TopLevelActors.Resolver = new AutoFacDependencyResolver(container, TopLevelActors.OrderSchedulerActorSystem);

                  TopLevelActors.MainActor = TopLevelActors.OrderSchedulerActorSystem.ActorOf(TopLevelActors.OrderSchedulerActorSystem.DI()
                          .Props<MainActor>()
                          .WithSupervisorStrategy(TopLevelActors.GetDefaultSupervisorStrategy),
                      nameof(MainActor));




              });




      
    }
}
