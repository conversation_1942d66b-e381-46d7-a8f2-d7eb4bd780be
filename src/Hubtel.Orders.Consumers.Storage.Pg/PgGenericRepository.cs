using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using Hubtel.Instrumentation;
using JustEat.StatsD;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Npgsql;
using NpgsqlTypes;
using Pluralize.NET;
using Exception = System.Exception;

namespace Hubtel.Orders.Consumers.Storage.Pg
{
    public class PgGenericRepository:DapperPgOrmBase,IGenericRepository
    {
        private readonly IStatsDPublisher _metrics;
        private readonly IHubtelDiagnosticsPublisher _aiPublisher;
        private readonly ILogger<PgGenericRepository> _logger;
        private readonly PgConfig _config;

        public PgGenericRepository(
            IStatsDPublisher metrics
            , IOptions<PgConfig> config
            , IHubtelDiagnosticsPublisher aiPublisher
            , ILogger<PgGenericRepository> logger)
        {
            
            _metrics = metrics;
            _aiPublisher = aiPublisher;
            _logger = logger;
            _config = config.Value;
        }

        private string GetTableName(Type t)
        {
            var tableName = string.Empty;

            var customAttributes = t.GetCustomAttributes(true);

            if (customAttributes.Any())
            {
                foreach (object attr in customAttributes)
                {
                    TableAttribute tableAttribute = attr as TableAttribute;
                    if (tableAttribute != null)
                    {
                        tableName = tableAttribute.Name;

                    }
                }
            }
            else
            {
                tableName = new Pluralizer().Pluralize(t.Name);
            }

            return $"\"{tableName}\"";

        }
        public async Task<IEnumerable<T>> GetAll<T>(string whereClause = "")
        {
            var table = GetTableName(typeof(T));

            using (_metrics.StartTimer($"queryall.{table}"))
            {
                using (var connection = new NpgsqlConnection(_config.ConnectionString))
                {
                    using (var p = _aiPublisher.StartPostgreSqlDiagnostics(connection))
                    {
                        try
                        {
                            var sql = $"SELECT * FROM {table} {whereClause}";
                            p.Sql = sql;
                        
                            return await connection.QueryAsync<T>(sql);
                        }
                        catch (Exception e)
                        {
                            p.Failed = true;
                            p.Exception = e;
                            throw;
                        }
                    }
                    
                }
            }
        }

        public async Task<IEnumerable<T>> Query<T>(string sql, object param = null)
        {
            var table = GetTableName(typeof(T));
            using (_metrics.StartTimer($"query.{table}"))
            {
                using (var connection = new NpgsqlConnection(_config.ConnectionString))
                {
                    using (var p = _aiPublisher.StartPostgreSqlDiagnostics(connection,sql,param))
                    {
                        try
                        {
                            return await connection.QueryAsync<T>(sql, param);
                        }
                        catch (Exception e)
                        {
                            p.Failed = true;
                            p.Exception = e;
                            throw;
                        }
                    }
                }
            }
          
        }

        public async Task<T> QuerySingle<T>(string sql, object param = null)
        {
            var table = GetTableName(typeof(T));
            using (_metrics.StartTimer($"querysingle.{table}"))
            {
                using (var connection = new NpgsqlConnection(_config.ConnectionString))
                {
                    using (var p = _aiPublisher.StartPostgreSqlDiagnostics(connection, sql, param))
                    {
                        
                        try
                        {
                            return await connection.QueryFirstOrDefaultAsync<T>(sql, param);
                        }
                        catch (Exception e)
                        {
                            p.Failed = true;
                            p.Exception = e;
                            throw;
                        }
                    }
                }
            }

        }

        public async Task<int> Execute(string sql, object param = null)
        {
            
            using (_metrics.StartTimer($"execute"))
            {
                using (var connection = new NpgsqlConnection(_config.ConnectionString))
                {
                    using (var p = _aiPublisher.StartPostgreSqlDiagnostics(connection, sql, param))
                    {

                        
                        try
                        {
                            return await connection.ExecuteAsync(sql, param);
                        }
                        catch (Exception e)
                        {
                            p.Failed = true;
                            p.Exception = e;
                            throw;
                        }
                    }
                }
            }
        }

        public async Task AddBulk<T>(List<T> items) where T : class
        {
            try
            {
                var theType = typeof(T);


                var tableName = GetTableName(theType);

              


                var fields = new Dictionary<string, (string, Type)>();
                PropertyInfo[] props = theType.GetProperties().OrderBy(a => a.Name).ToArray();
                foreach (PropertyInfo prop in props)
                {

                    object[] attrs = prop.GetCustomAttributes(true);
                    if (attrs.Any())
                    {
                        foreach (object attr in attrs)
                        {
                            NotMappedAttribute mappedAttribute = attr as NotMappedAttribute;
                            if (mappedAttribute != null)
                            {
                                continue;
                            }

                            PrimaryKeyAttribute primaryKeyAttribute = attr as PrimaryKeyAttribute;
                            if (primaryKeyAttribute != null)
                            {
                                if (primaryKeyAttribute.IncludeInInsert)
                                {
                                    fields[prop.Name] = (primaryKeyAttribute.Name, prop.PropertyType);
                                }

                                continue;
                            }
                            ColumnAttribute columnAttribute = attr as ColumnAttribute;
                            if (columnAttribute != null)
                            {
                                fields[prop.Name] = (columnAttribute.Name, prop.PropertyType);
                            }
                        }
                    }
                    else
                    {
                        fields[prop.Name] = (prop.Name, prop.PropertyType);
                    }



                }


                var sqlBuilder = new StringBuilder();
                sqlBuilder.Append("(");
                foreach (var field in fields)
                {
                    sqlBuilder.Append($"\"{field.Value.Item1}\",");
                }

                var sql = sqlBuilder.ToString();
                if (!string.IsNullOrEmpty(sql))
                {
                    sql = sql.Substring(0, sql.Length - 1);//remove trailing ','
                    sql = sql + ")";

                }

                var finalSql = $"COPY {tableName} {sql} FROM STDIN (FORMAT BINARY)";

                _logger.LogInformation($"final SQL is {finalSql}");
                using (var conn = new NpgsqlConnection(_config.ConnectionString))

                {
                    await conn.OpenAsync();
                    using (var p = _aiPublisher.StartPostgreSqlDiagnostics(conn, finalSql))
                    {

                        try
                        {
                            using (var bulkImporter = conn.BeginBinaryImport(finalSql))
                            {
                                foreach (var record in items)
                                {

                                    var recordFields = record.GetType().GetProperties().OrderBy(a => a.Name).ToArray();

                                    bulkImporter.StartRow();

                                    foreach (var propertyInfo in recordFields)
                                    {
                                        if (!fields.ContainsKey(propertyInfo.Name))
                                        {
                                            continue;
                                        }

                                        var fieldInfo = fields[propertyInfo.Name];


                                        if (fieldInfo.Item2 == typeof(string))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Varchar);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(decimal))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Numeric);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(decimal?))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Numeric);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(double))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Double);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(double?))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Double);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(bool))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Boolean);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(bool?))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Boolean);
                                            }
                                        }

                                        else if (fieldInfo.Item2 == typeof(float))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Real);
                                            }
                                        }

                                        else if (fieldInfo.Item2 == typeof(float?))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Real);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(long))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Bigint);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(long?))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Bigint);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(DateTime))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Timestamp);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(DateTime?))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Timestamp);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(Guid))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Uuid);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(Guid?))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Uuid);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(short))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Smallint);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(short?))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Smallint);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(Int16))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Smallint);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(Int16?))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Smallint);
                                            }
                                        }

                                        else if (fieldInfo.Item2 == typeof(Int32))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Integer);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(Int32?))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Integer);
                                            }
                                        }

                                        else if (fieldInfo.Item2 == typeof(Int64))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Bigint);
                                            }
                                        }
                                        else if (fieldInfo.Item2 == typeof(Int64?))
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Bigint);
                                            }
                                        }
                                        else if (fieldInfo.Item2.IsEnum)
                                        {

                                            var dbValue = propertyInfo.GetValue(record);

                                            object underlyingValue = Convert.ChangeType(dbValue,
                                                Enum.GetUnderlyingType(fieldInfo.Item2));
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(underlyingValue);
                                            }
                                        }
                                        else
                                        {
                                            var dbValue = propertyInfo.GetValue(record);
                                            if (dbValue == null)
                                            {
                                                bulkImporter.WriteNull();
                                            }
                                            else
                                            {
                                                bulkImporter.Write(dbValue, NpgsqlDbType.Text);
                                            }
                                        }
                                    }


                                }

                                bulkImporter.Complete();
                                //await conn.CloseAsync();
                            }
                        }
                        catch (Exception e)
                        {
                            p.Failed = true;
                            p.Exception = e;
                            throw;
                        }
                        finally
                        {
                            await conn.CloseAsync();
                        }


                    }
                


                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, e.Message);
            }

            //return Task.CompletedTask;
        }

        public Task AddBulk<T>(string tableName, List<T> items) where T : class
        {
            throw new NotImplementedException();
        }
    }
}