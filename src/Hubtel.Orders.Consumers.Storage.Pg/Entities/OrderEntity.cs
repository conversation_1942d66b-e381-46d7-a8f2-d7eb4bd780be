using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Hubtel.Orders.Consumers.Storage.Pg.Entities
{
    [Table("Orders")]
    public class OrderEntity
    {
        public string Id { get; set; }
        public string BusinessId { get; set; }
        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        public string CreatedBy { get; set; }

        public string UpdatedBy { get; set; }
        public string IntegrationChannel { get; set; }

        public string PosDeviceId { get; set; }
    
        public string PosDeviceType { get; set; }

        public DateTime OrderDate { get; set; }

        public string OrderNumber { get; set; }

        public string Note { get; set; }

        public string Description { get; set; }

        public string Status { get; set; } //PAID, PARITALLY PAID, UNPAID, CANCELLED

        public string AssignedTo { get; set; }

        public string EmployeeId { get; set; }

        public string EmployeeName { get; set; }

        public string CustomerMobileNumber { get; set; }

        public string CustomerName { get; set; }

        public string BranchId { get; set; }

        public string BranchName { get; set; }

        
      //  public float? TaxRate { get; set; }

        public float? DiscountRate { get; set; }


        public decimal? DiscountAmount { get; set; }


        public decimal Subtotal { get; set; }


        public decimal TotalAmountDue { get; set; }

        /// <summary>
        /// This is a total of all payments
        /// </summary>
        public decimal AmountPaid { get; set; }

        /// <summary>
        /// This is a total of all refunds
        /// </summary>
        public decimal AmountRefunded { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PaymentTypes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool? IsFulfilled { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ConsumerFeedback { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int ConsumerRating { get; set; }

        public string BusinessEmail { get; set; }

        public string BusinessMobileNumber { get; set; }

        public string CustomerEmail { get; set; }


        public string FcmCustomer { get; set; }

        public string FcmDevice { get; set; }


        public decimal AmountDueProducer { get; set; }


        public decimal DeliveryFee { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool HasDelivery { get; set; }

        public string BranchEmail { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BranchPhoneNumber { get; set; }

        public string BusinessName { get; set; }
        
        public double CustomerReward { get; set; }

        public string SenderId { get; set; }

        public string ReturnUrl { get; set; }
  
        public string LogoUrl { get; set; }

        public string CancellationUrl { get; set; }

        /// <summary>
        /// Human readable location of a delivery, e.g. Kokomlemle
        /// </summary>
        public string DeliveryLocationName { get; set; }
        
        /// <summary>
        /// Indicates whether the invoice was created due to a recurring payment
        /// </summary>
        public bool IsRecurring { get; set; }
        /// <summary>
        /// RecurringInvoiceID
        /// </summary>
        public string RecurringInvoiceId { get; set; }
        /// <summary>
        /// Determines whether order is for a service so that certain services will be rendered
        /// </summary>
        public bool IsProgrammableService { get; set; }
        /// <summary>
        /// Total profit made on the sale
        /// </summary>
        public decimal TotalProfit { get; set; }
        public string Country { get; set; }
      
        public string Region { get; set; }
    
        public string City { get; set; }
  
        public string Zone { get; set; }
   
        public string Station { get; set; }
        
        public string Longitude { get; set; }
        public string Latitude { get; set; }
    }
}