using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Hubtel.Orders.Consumers.Storage.Pg.Entities
{
    [Table("OrderInvoiceAdditions")]
    public class InvoiceAdditionEntity
    {
        public string Id { get; set; }
        public string BusinessId { get; set; }
        public string BranchId { get; set; }
        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        public string CreatedBy { get; set; }

        public string UpdatedBy { get; set; }

        public string OrderId { get; set; }
        public string Name { get; set; }
        public bool? IsFlatFee { get; set; }
        public decimal? Figure { get; set; }
        public string CalculationMethod { get; set; }
        public decimal ComputedValue { get; set; }
        public bool? IsActive { get; set; }
        public short? Sequence { get; set; }
        public bool? IsInclusive { get; set; }
    }
}