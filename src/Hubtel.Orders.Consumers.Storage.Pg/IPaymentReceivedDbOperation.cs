using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Hubtel.Orders.Consumers.Common;
using Hubtel.Orders.Consumers.Storage.Pg.Entities;
using Microsoft.Extensions.Logging;

namespace Hubtel.Orders.Consumers.Storage.Pg
{
    public interface IPaymentReceivedDbOperation
    {
        Task<bool> PersistOrders(List<OrderEntity> orders);
        Task<bool> PersistOrderItems(List<OrderItemsEntity> orderItems);
        Task<bool> PersistInvoiceAdditions(List<InvoiceAdditionEntity> invoiceAdditions);
        Task<bool> PersistPayments(List<PaymentEntity> payments);
    }

    public class PaymentReceivedDbOperation: IPaymentReceivedDbOperation
    {
        private readonly IGenericRepository _repo;
        private readonly ILogger<PaymentReceivedDbOperation> _logger;

        public PaymentReceivedDbOperation(IGenericRepository repo,ILogger<PaymentReceivedDbOperation> logger)
        {
            _repo = repo;
            _logger = logger;
        }
        public async Task<bool> PersistOrders(List<OrderEntity> orders)
        {
            try
            {
                if (orders.Count > 500)
                {
                    _logger.LogDebug($"newOrders size is {orders.Count}");
                    var chunkSize = FindChunckSize(orders.Count, 500);

                    _logger.LogDebug($"will break it into chunks of {chunkSize} containing about 500 rows each");
                    var parts = orders.IntoChunks(500);


                    foreach (var part in parts)
                    {
                        _logger.LogDebug($"about to insert {part.Count} chunked rows");
                        await _repo.AddBulk(part);
                    }
                }
                else
                {

                    await _repo.AddBulk(orders);
                }

                return true;
            }
            catch (Exception e)
            {
                //todo: we don't know what to do yet
                _logger.LogError("An exception happened when persisting order information to database: \n" +
                                 $"Exception message: {e.Message} \n" +
                                 $"Full exception: {e}");
                return false;
            }
        }

        public async Task<bool> PersistOrderItems(List<OrderItemsEntity> orderItems)
        {
            try
            {
                if (orderItems.Count > 500)
                {
                    _logger.LogDebug($"order items size is {orderItems.Count}");
                    var chunkSize = FindChunckSize(orderItems.Count, 500);

                    _logger.LogDebug($"will break it into chunks of {chunkSize} containing about 500 rows each");
                    var parts = orderItems.IntoChunks(500);


                    foreach (var part in parts)
                    {
                        _logger.LogDebug($"about to insert {part.Count} chunked rows for order items");
                        await _repo.AddBulk(part);
                    }
                }
                else
                {

                    await _repo.AddBulk(orderItems);
                }

                return true;
            }
            catch (Exception e)
            {
                _logger.LogError("An exception happened when persisting order items to database: \n" +
                                 $"Exception message: {e.Message} \n" +
                                 $"Full exception: {e}");
                return false;
            }
        }

        public async Task<bool> PersistInvoiceAdditions(List<InvoiceAdditionEntity> invoiceAdditions)
        {

            try
            {
                if (invoiceAdditions.Count > 500)
                {
                    _logger.LogDebug($"invoiceAdditions size is {invoiceAdditions.Count}");
                    var chunkSize = FindChunckSize(invoiceAdditions.Count, 500);

                    _logger.LogDebug($"will break it into chunks of {chunkSize} containing about 500 rows each");
                    var parts = invoiceAdditions.IntoChunks(500);


                    foreach (var part in parts)
                    {
                        _logger.LogDebug($"about to insert {part.Count} chunked rows for invoiceAdditions");
                        await _repo.AddBulk(part);
                    }
                }
                else
                {

                    await _repo.AddBulk(invoiceAdditions);
                }

                return true;
            }
            catch (Exception e)
            {

                _logger.LogError("An exception happened when persisting invoice additions to database: \n" +
                                 $"Exception message: {e.Message} \n" +
                                 $"Full exception: {e}");
                return false;
            }
        }

        public async Task<bool> PersistPayments(List<PaymentEntity> payments)
        {
            try
            {
                if (payments.Count > 500)
                {
                    _logger.LogDebug($"payments size is {payments.Count}");
                    var chunkSize = FindChunckSize(payments.Count, 500);

                    _logger.LogDebug($"will break it into chunks of {chunkSize} containing about 500 rows each");
                    var parts = payments.IntoChunks(500);


                    foreach (var part in parts)
                    {
                        _logger.LogDebug($"about to insert {part.Count} chunked rows");
                        await _repo.AddBulk(part);
                    }
                }
                else
                {
                    await _repo.AddBulk(payments);
                }

                return true;
            }
            catch (Exception e)
            {
                _logger.LogError("An exception happened when persisting payments to database: \n" +
                                 $"Exception message: {e.Message} \n" +
                                 $"Full exception: {e}");
                return false;
            }
        }

        private int FindChunckSize(int colSize, int batchInsertSize = 500)
        {
            if (batchInsertSize >= colSize)
            {
                return 1;
            }
            var remainder = colSize - batchInsertSize;
            var chunkSize = 0;
            while (remainder > batchInsertSize)
            {
                chunkSize++;
                remainder = remainder - batchInsertSize;
            }

            return chunkSize + 1;
        }
    }

}