using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Reflection;

namespace Hubtel.Orders.Consumers.Storage.Pg
{
    public abstract class DapperPgOrmBase
    
    {

        protected string GetInsertString(Object item, string tableName, string foreignKeyName = "id")
        {


            var attr = GetProperties(item);

            var sql =
                $"INSERT INTO {tableName} ({string.Join(",", attr.Where(f => f.Value != foreignKeyName).Select(a => "\"" + a.Key + "\""))}) VALUES ({string.Join(",", attr.Where(f => f.Value != foreignKeyName).Select(a => "@" + a.Key))})";


            return sql;
        }

        protected string GetUpdateString(Object item, string tableName, string foreignKeyName = "id", string foreignKeyPropertyName = "Id")
        {
            var attr = GetProperties(item);

            var sql =
                $"UPDATE {tableName} SET {string.Join(",", attr.Where(f => "\"" + f.Key + "\"" != foreignKeyName).Select(a => "\"" + a.Key + "\"" + "=@" + a.Key))} WHERE {foreignKeyName}=@{foreignKeyPropertyName}";


            return sql;
        }



        private Dictionary<string, string> GetProperties(Object item)
        {
            PropertyInfo[] props = item.GetType().GetProperties();

            var _dict = new Dictionary<string, string>();

            foreach (PropertyInfo prop in props)
            {

                object[] attrs = prop.GetCustomAttributes(true);
                if (attrs.Any())
                {
                    foreach (object attr in attrs)
                    {
                        ColumnAttribute authAttr = attr as ColumnAttribute;
                        if (authAttr != null)
                        {
                            string propName = prop.Name;
                            string auth = authAttr.Name;

                            _dict[propName] = auth;
                        }
                    }
                }
                else
                {
                    _dict[prop.Name] = prop.Name;
                }



            }

            return _dict;
        }

       
    }
}