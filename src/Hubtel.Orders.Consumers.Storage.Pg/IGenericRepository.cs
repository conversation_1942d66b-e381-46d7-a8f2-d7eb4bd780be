using System.Collections.Generic;
using System.Threading.Tasks;

namespace Hubtel.Orders.Consumers.Storage.Pg
{
    public interface IGenericRepository
    {
        Task<IEnumerable<T>> GetAll<T>(string whereClause="");
        Task<IEnumerable<T>> Query<T>(string sql, object param=null);
        Task<T> QuerySingle<T>(string sql, object param=null);

        Task<int> Execute(string sql, object param = null);
        Task AddBulk<T>(List<T> items) where T : class;
        
        Task AddBulk<T>(string tableName,List<T> items) where T : class;
        
    }
}
