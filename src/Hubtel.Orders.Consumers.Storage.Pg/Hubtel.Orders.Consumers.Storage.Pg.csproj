<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>


  <ItemGroup>

    <PackageReference Include="Pluralize.NET" Version="1.0.2" />
    <PackageReference Include="Dapper" Version="2.0.35" />
    <PackageReference Include="DapperExtensions.DotnetCore" Version="1.0.1" />

    <PackageReference Include="JustEat.StatsD" Version="4.1.0" />

    <PackageReference Include="System.Data.SqlClient" Version="4.8.2" />
    <PackageReference Include="Npgsql" Version="4.1.5" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="3.1.4" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="3.1.6" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="3.1.6" />
    <PackageReference Include="Hubtel.Instrumentation" Version="1.0.15" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\Hubtel.Orders.Consumers.Common\Hubtel.Orders.Consumers.Common.csproj" />
  </ItemGroup>

</Project>
